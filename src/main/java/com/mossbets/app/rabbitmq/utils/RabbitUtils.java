package com.mossbets.app.rabbitmq.utils;

import com.mossbets.app.utils.JedisUtils;
import com.mossbets.app.utils.Utilities;
import io.agroal.api.AgroalDataSource;
import jakarta.enterprise.context.ApplicationScoped;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import org.codehaus.jettison.json.JSONObject;

@ApplicationScoped
public class RabbitUtils {

    /**
     * LogUserActions
     *
     * @param con
     * @param userId
     * @param activity
     * @param request
     * @return
     */
    public static boolean LogUserActions(final AgroalDataSource con, long userId,
            String activity, String request) {
        boolean logged = false;
        String insertLogs = "INSERT INTO `mossbets_user`.`user_logs`"
                + "(`user_logs`.`user_id`,`user_logs`.`activity`,`user_logs`.`request`"
                + ",`user_logs`.`created_at`) VALUES(?,?,?,NOW())";
        try (final java.sql.Connection dbW = con.getConnection();
                final PreparedStatement ps = dbW.prepareStatement(
                        insertLogs, PreparedStatement.RETURN_GENERATED_KEYS);) {
            dbW.setAutoCommit(false);
            ps.setLong(1, userId);
            ps.setString(2, activity);
            ps.setString(3, request);
            ps.setInt(5, 1);
            long id = ps.executeUpdate();
            try (ResultSet rs = ps.getGeneratedKeys()) {
                if (rs.next()) {
                    id = rs.getLong(1);
                }
            }

            if (id < 1) {
                dbW.rollback();
                throw new SQLException("UserLogs for UserId:" + userId + " failed!");
            }
            dbW.commit();
        } catch (SQLException e) {
        }

        return logged;
    }

    /**
     * GetWalletBalance
     *
     * @param con
     * @param profileId
     * @return
     */
    public static Map<String, Double> GetWalletBalance(final AgroalDataSource con, long profileId) {
        Map<String, Double> bal = null;
        try (final java.sql.Connection wrDb = con.getConnection();
                final PreparedStatement ps = wrDb.prepareStatement(
                        "SELECT IFNULL(balance,0) as balance, IFNULL(bonus,0) as bonus"
                        + ",status FROM profile_balance WHERE profile_id =? LIMIT 1");) {
            ps.setLong(1, profileId);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    bal = new HashMap<>();
                    bal.put("balance", rs.getDouble("balance"));
                    bal.put("bonus", rs.getDouble("bonus"));
                }

            } catch (SQLException e) {
                throw e;
            }
        } catch (Exception e) {
        }

        if (null == bal) {
            bal = new HashMap<>();
            bal.put("balance", 0.0);
            bal.put("bonus", 0.0);
        }

        return bal;
    }

    /**
     * getCustomerDetails
     *
     * @param profileId
     * @param dbConn
     * @return
     */
    public static Map<String, Object> getCustomerDetails(long profileId, final Connection dbConn) {
        Map<String, Object> customer = null;
        try (final PreparedStatement ps = dbConn.prepareStatement(
                "SELECT `p`.`msisdn`,`p`.`name`,`p`.`acc_number`,`p`.`hash`,`p`.`network`"
                + ",IFNULL(`pb`.`status`,5) as status,IFNULL(`pb`.`balance`,0) as bal"
                + ",IFNULL(`pb`.`bonus`,0)as bon FROM `mossbets_profile`.`profile` p "
                + "LEFT JOIN `mossbets_profile`.`profile_balance` pb ON `p`.`id`=`pb`.`profile_id` "
                + "WHERE `p`.`status`=1 AND `p`.`id`=?");) {
            ps.setLong(1, profileId);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    customer = new HashMap<>();
                    customer.put("msisdn", rs.getString("msisdn"));
                    customer.put("name", rs.getString("name"));
                    customer.put("accNumber", rs.getString("acc_number"));
                    customer.put("balance", rs.getDouble("bal"));
                    customer.put("bonus", rs.getDouble("bon"));
                    customer.put("status", rs.getInt("status"));
                    customer.put("network", "");
                    if (!Utilities.isBlank(rs.getString("network"))) {
                        customer.put("network", rs.getString("network"));
                    }
                }
            } catch (SQLException e) {
                throw e;
            }

        } catch (Exception e) {
        }

        return customer;
    }

    /**
     * getWithdrawalRequest
     *
     * @param requestId
     * @param dbConn
     * @return
     */
    public static Map<String, Object> getWithdrawalRequest(long requestId, final Connection dbConn) {
        Map<String, Object> request = null;
        try (final PreparedStatement ps = dbConn.prepareStatement(
                "SELECT id,profile_id,currency,withdraw_amount,charges,withholding_tax"
                + ",reference_id,response_code,service_status,ip_address"
                + ",channel_source,retries FROM withdrawal_request WHERE id=?");) {
            ps.setLong(1, requestId);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    request = new HashMap<>();
                    request.put("profileId", rs.getLong("profile_id"));
                    request.put("currency", rs.getString("currency"));
                    request.put("amount", rs.getDouble("withdraw_amount"));
                    request.put("withholdingTax", rs.getDouble("withholding_tax"));
                    request.put("charges", rs.getDouble("charges"));
                    request.put("referenceId", rs.getString("reference_id"));
                    request.put("responseCode", rs.getInt("response_code"));
                    request.put("status", rs.getInt("service_status"));
                    request.put("retries", rs.getInt("retries"));
                    request.put("ipAddress", rs.getString("ip_address"));
                }
            } catch (SQLException e) {
                throw e;
            }
        } catch (Exception e) {
        }

        return request;
    }

    /**
     * getWithdrawalDlrRequest
     *
     * @param requestId
     * @param dbConn
     * @return
     */
    public static Map<String, Object> getWithdrawalDlrRequest(long requestId, final Connection dbConn) {
        Map<String, Object> request = null;
        try (final PreparedStatement ps = dbConn.prepareStatement(
                "SELECT id,response_code,response_status,response_description"
                + ",receipt_number,processed,extra_data FROM withdrawal_dlr WHERE request_id=?");) {
            ps.setLong(1, requestId);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    request = new HashMap<>();
                    request.put("withdrawId", rs.getLong("id"));
                    request.put("responseCode", rs.getString("response_code"));
                    request.put("responseStatus", rs.getString("response_status"));
                    request.put("responseDescription", rs.getString("response_description"));
                    request.put("receiptNumber", rs.getString("receipt_number"));
                    request.put("processed", rs.getInt("processed"));
                    request.put("extraData", rs.getString("extra_data"));
                }
            } catch (SQLException e) {
                throw e;
            }
        } catch (Exception e) {
        }

        return request;
    }

    /**
     * updateStatements
     *
     * @param con
     * @param sql
     * @param params
     * @return
     */
    @SuppressWarnings("CallToPrintStackTrace")
    public static boolean updateStatements(final AgroalDataSource con, String sql, Object[] params) {
        boolean status = false;

        try (final java.sql.Connection wrDb = con.getConnection();
                final PreparedStatement ps = wrDb.prepareStatement(sql);) {

            wrDb.setAutoCommit(false);
            for (int i = 0; i < params.length; i++) {
                int index = i + 1;
                if (params[i] instanceof String) {
                    ps.setString(index, (String) params[i]);
                    continue;
                }

                if (params[i] instanceof Integer) {
                    ps.setInt(index, (int) params[i]);
                    continue;
                }

                if (params[i] instanceof Long) {
                    ps.setLong(index, (long) params[i]);
                }
            }

            int updateState = ps.executeUpdate();
            wrDb.commit();

            if (updateState > 0) {
                return true;
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }

        return status;
    }

    /**
     * getBetLimits
     *
     * @param clientId
     * @param currency
     * @param providerName
     * @param dbConn
     * @return
     */
    public static Map<String, Double> getBetLimits(int clientId, String currency,
            String providerName, final Connection dbConn) {
        Map<String, Double> limits = null;
        try (final PreparedStatement ps = dbConn.prepareStatement(
                "SELECT max_win,risk_approval_amount FROM bet_limits "
                + "WHERE status=1 AND currency=? AND bet_name=? AND client_id=?");) {
            ps.setString(1, currency);
            ps.setString(2, providerName);
            ps.setInt(3, clientId);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    limits = new HashMap<>();
                    limits.put("maxWin", rs.getDouble("max_win"));
                    limits.put("riskAmount", rs.getDouble("risk_approval_amount"));
                }
            } catch (SQLException e) {
                throw e;
            }
        } catch (Exception e) {
        }

        return limits;
    }

    /**
     * getBetAudits
     *
     * @param clientId
     * @param betType
     * @param betName
     * @param dbConn
     * @return
     */
    public static boolean getBetAudits(int clientId, int betType, String betName, final Connection dbConn) {
        boolean canAudit = false;
        try (final PreparedStatement ps = dbConn.prepareStatement(
                "SELECT IFNULL(status,0) as status FROM bets_audits "
                + "WHERE bet_type=? AND bet_name=? AND client_id=?");) {
            ps.setInt(1, clientId);
            ps.setString(2, betName);
            ps.setInt(3, clientId);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    if (rs.getInt("status") == 1) {
                        canAudit = true;
                    }
                }
            } catch (SQLException e) {
                throw e;
            }
        } catch (Exception e) {
        }

        return canAudit;
    }

    /**
     * getSportsBetSlipDetails
     *
     * @param betId
     * @param dbConn
     * @return
     */
    public static Map<String, Object> getSportsBetSlipDetails(long betId, final Connection dbConn) {
        Map<String, Object> slip = null;
        try (final PreparedStatement ps = dbConn.prepareStatement(
                "SELECT odd_value,status FROM sports_bet_slip WHERE status IN (1,7) "
                + "AND bet_id=?");) {
            ps.setLong(1, betId);
            try (ResultSet rs = ps.executeQuery()) {
                double odds = 1;
                int count = 0;
                while (rs.next()) {
                    count++;
                    if (rs.getInt("status") == 7) {
                        odds *= 1;
                        continue;
                    }

                    odds *= rs.getDouble("odd_value");
                }

                if (count > 0) {
                    slip = new HashMap<>();
                    slip.put("odds", Utilities.Round2Decimal(odds));
                    slip.put("count", count);
                }
            } catch (SQLException e) {
                throw e;
            }
        } catch (Exception e) {
            slip = null;
        }

        return slip;
    }

    /**
     * getSportsBetDetails
     *
     * @param betId
     * @param dbConn
     * @return
     */
    public static Map<String, Object> getSportsBetDetails(long betId, final Connection dbConn) {
        Map<String, Object> bets = null;
        try (final PreparedStatement ps = dbConn.prepareStatement(
                "SELECT s.client_id,s.profile_id,s.bet_transaction_id,s.risk_state"
                + ",IFNULL(s.bet_credit_transaction_id,0) as creditId,s.status"
                + ",s.extra_data,s.bet_reference,s.possible_win,s.bet_currency"
                + ",s.bet_amount,s.kra_report,s.total_odd,s.bet_type,s.total_games"
                + ",s.witholding_tax,s.excise_tax,s.created_at FROM sports_bet s "
                + "WHERE bet_id=?");) {
            ps.setLong(1, betId);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    bets = new HashMap<>();
                    bets.put("clientId", rs.getInt("client_id"));
                    bets.put("profileId", rs.getLong("profile_id"));
                    bets.put("odds", rs.getDouble("total_odd"));
                    bets.put("debitId", rs.getLong("bet_transaction_id"));
                    bets.put("creditId", rs.getLong("creditId"));
                    bets.put("totalGames", rs.getInt("total_games"));
                    bets.put("riskState", rs.getInt("risk_state"));
                    bets.put("betType", rs.getInt("bet_type"));
                    bets.put("status", rs.getInt("status"));
                    bets.put("kraReport", rs.getInt("kra_report"));
                    bets.put("eData", rs.getString("extra_data"));
                    bets.put("betReference", rs.getString("bet_reference"));
                    bets.put("currency", rs.getString("bet_currency"));
                    bets.put("payout", Utilities.Round2Decimal(
                            rs.getDouble("possible_win")));
                    bets.put("stake", rs.getDouble("bet_amount"));
                    bets.put("exciseTax", rs.getDouble("excise_tax"));
                    bets.put("wTax", rs.getDouble("witholding_tax"));
                    bets.put("createdAt", rs.getString("created_at"));
                }
            } catch (SQLException e) {
                throw e;
            }
        } catch (Exception e) {
        }

        return bets;
    }

    /**
     * getFirstDeposit
     *
     * @param profileId
     * @param refTypeId
     * @param trxnTypeId
     * @param source
     * @param dbConn
     * @return
     */
    public static Map<String, Object> getFirstDeposit(long profileId, int refTypeId,
            int trxnTypeId, String source, final Connection dbConn) {
        Map<String, Object> deposit = new HashMap<>();
        try (final PreparedStatement ps = dbConn.prepareStatement(
                "SELECT IFNULL(t.id,0) as trx_count,ifnull(sum(t.amount),0) as trx_sum "
                + "FROM transaction t WHERE date(t.created_at)=date(now()) "
                + "AND t.profile_id=? AND t.reference_type_id=? "
                + "AND t.transaction_type_id=? AND t.source=?");) {
            ps.setLong(1, profileId);
            ps.setInt(2, refTypeId);
            ps.setInt(3, trxnTypeId);
            ps.setString(4, source);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    deposit = new HashMap<>();
                    deposit.put("count", rs.getInt("trx_count"));
                    deposit.put("amount", rs.getDouble("trx_sum"));
                }

                if (deposit.size() < 1) {
                    deposit.put("count", 0);
                    deposit.put("amount", 0.0);
                }
            } catch (SQLException e) {
                throw e;
            }
        } catch (Exception e) {

        }

        return deposit;
    }

    /**
     * getBonusDetails
     *
     * @param promoId
     * @param dbConn
     * @return
     */
    public static Map<String, Object> getBonusDetails(long promoId, final Connection dbConn) {
        Map<String, Object> user = null;
        try (final PreparedStatement ps = dbConn.prepareStatement(
                "SELECT ps.bonus_amount,ps.max_win,ps.expiry_period"
                + ",p.promo_name,pt.component FROM mossbets_bonus.promotion_settings ps "
                + "JOIN mossbets_bonus.promotion p ON ps.promo_id=p.id "
                + "JOIN mossbets_bonus.promotion_type pt ON p.promo_type_id=pt.id "
                + "WHERE ps.status=1 AND p.status=1 AND pt.status=1 "
                + "AND NOW()>=p.starting_date AND p.ending_date>=NOW() "
                + "AND ps.id=?")) {
            ps.setLong(1, promoId);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    user = new HashMap<>();
                    user.put("bonus", rs.getDouble("bonus_amount"));
                    user.put("maxWin", rs.getDouble("max_win"));
                    user.put("expiryPeriod", rs.getInt("expiry_period"));
                    user.put("promoName", rs.getString("promo_name"));
                    user.put("component", rs.getString("component"));
                }
            } catch (SQLException e) {
                throw e;
            }
        } catch (Exception e) {
        }

        return user;
    }

    /**
     * getUserDetails
     *
     * @param userId
     * @param dbConn
     * @return
     */
    public static Map<String, Object> getUserDetails(long userId, final Connection dbConn) {
        Map<String, Object> user = null;
        try (final PreparedStatement ps = dbConn.prepareStatement(
                "SELECT `u`.`user_name`,`u`.`display_name` FROM `mossbets_user`.`user` u "
                + "WHERE `u`.`status`=1 AND `u`.`id`=?");) {
            ps.setLong(1, userId);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    user = new HashMap<>();
                    user.put("email", rs.getString("user_name"));
                    user.put("name", rs.getString("display_name"));
                }
            } catch (SQLException e) {
                throw e;
            }
        } catch (Exception e) {
        }

        return user;
    }

    /**
     * getPendingApprovals Status(1-Approved,3-Rejected)
     *
     * @param pendingId
     * @param dbConn
     * @return
     */
    public static Map<String, Object> getPendingApprovals(long pendingId, final Connection dbConn) {
        Map<String, Object> trxn = null;
        try (final PreparedStatement ps = dbConn.prepareStatement(
                "SELECT p.profile_id,p.transaction_id,p.currency,p.amount,p.source"
                + ",p.description,p.reason,p.approved_by FROM transaction_pending_approval p "
                + "WHERE p.status=1 AND p.id=? AND p.completed_on is null");) {
            ps.setLong(1, pendingId);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    trxn = new HashMap<>();
                    trxn.put("profileId", rs.getLong("profile_id"));
                    trxn.put("transactionId", rs.getLong("transaction_id"));
                    trxn.put("currency", rs.getString("currency"));
                    trxn.put("amount", rs.getDouble("amount"));
                    trxn.put("source", rs.getString("source"));
                    trxn.put("approved_by", rs.getInt("approved_by"));
                    trxn.put("reason", rs.getString("reason"));
                    trxn.put("description", rs.getString("description"));
                }
            } catch (SQLException e) {
                throw e;
            }
        } catch (Exception e) {
        }

        return trxn;
    }

    /**
     * createSMSOutBox
     *
     * @param profileId
     * @param shortCode
     * @param message
     * @param messageType
     * @param dbConn
     * @return
     */
    public static long createSMSOutBox(long profileId, String shortCode, String message,
            String messageType, final AgroalDataSource dbConn) {

        String outboxSql = "INSERT INTO profile_outbox(profile_id,sender_id,message_type"
                + ",message,status,created_at) VALUES(?,?,?,?,?,NOW())";

        long outbox = 0;
        try (final java.sql.Connection dbW = dbConn.getConnection();
                final PreparedStatement ps = dbW.prepareStatement(
                        outboxSql, PreparedStatement.RETURN_GENERATED_KEYS);) {
            dbW.setAutoCommit(false);
            ps.setLong(1, profileId);
            ps.setString(2, shortCode);
            ps.setString(3, messageType);
            ps.setString(4, message);
            ps.setInt(5, 1);
            outbox = ps.executeUpdate();
            try (ResultSet rs = ps.getGeneratedKeys()) {
                if (rs.next()) {
                    outbox = rs.getLong(1);
                }
            }

            if (outbox < 1) {
                dbW.rollback();
                throw new SQLException(messageType + " Sms Outbox for profileId:" + profileId);
            }
            dbW.commit();
        } catch (SQLException e) {
        }

        return outbox;
    }

    /**
     * getPaybillSettings
     *
     * @param redisKey
     * @param paybillNumber
     * @param dbConn
     * @return
     */
    public static Map<String, String> getPaybillSettings(String redisKey,
            String paybillNumber, final AgroalDataSource dbConn) {
        Map<String, String> pbConfig = null;
        try (final java.sql.Connection db = dbConn.getConnection();
                final PreparedStatement ps = db.prepareStatement(
                        "SELECT ps.id settingId,ps.initiator_name,ps.secret_key"
                        + ",ps.consumer_key,ps.service_app_key pass_key "
                        + "FROM paybill_settings ps JOIN paybills p ON ps.paybill_id=p.id "
                        + "WHERE p.status=1 AND p.paybill_type=1 AND p.paybill_number=?");) {
            ps.setString(1, paybillNumber);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    pbConfig = new HashMap<>();
                    pbConfig.put("settingId", rs.getString("settingId"));
                    pbConfig.put("initiator", rs.getString("initiator_name"));
                    pbConfig.put("secretKey", rs.getString("secret_key"));
                    pbConfig.put("consumerKey", rs.getString("consumer_key"));
                    pbConfig.put("passKey", rs.getString("pass_key"));
                    pbConfig.put("type", "CustomerPayBillOnline");

                    JedisUtils.saveData(
                            redisKey,
                            new JSONObject(pbConfig).toString(),
                            (60 * 60));
                }
            } catch (SQLException e) {
                throw e;
            }
        } catch (Exception e) {
        }

        return pbConfig;
    }

    /**
     * getMSISDNviaHash
     *
     * @param msisdnHash
     * @param dbConn
     * @return
     */
    public static Map<String, Object> getMSISDNviaHash(String msisdnHash, final Connection dbConn) {
        Map<String, Object> profile = null;
        try (final PreparedStatement ps = dbConn.prepareStatement(
                "SELECT p.id profile_id,IFNULL(pb.id,0) as acc_id,p.msisdn,p.name"
                + ",p.network,p.acc_number "
                + "FROM profile p LEFT JOIN profile_balance pb ON p.id=pb.profile_id "
                + "WHERE p.status=1 AND p.hash=? OR p.msisdn=?");) {
            ps.setString(1, msisdnHash);
            ps.setString(2, msisdnHash);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    profile = new HashMap<>();
                    profile.put("profileId", rs.getLong("profile_id"));
                    profile.put("msisdn", rs.getLong("msisdn"));
                    profile.put("accNo", rs.getString("acc_number"));
                    profile.put("network", rs.getString("network"));
                    profile.put("accId", rs.getLong("acc_id"));

                    profile.put("name", "");
                    if (!Utilities.isBlank(rs.getString("name"))) {
                        profile.put("name", rs.getString("name"));
                    }
                }
            } catch (SQLException e) {
                throw e;
            }
        } catch (Exception e) {
        }

        return profile;
    }

    /**
     * checkDuplicateMpesaTrxnExist
     *
     * @param trxnCode
     * @param db
     * @return
     */
    public static boolean checkDuplicateMpesaTrxnExist(String trxnCode, final AgroalDataSource db) {
        boolean status = false;
        try (final java.sql.Connection conn = db.getConnection();
                final PreparedStatement ps = conn.prepareStatement("SELECT COUNT(id) AS trx_count "
                        + "FROM payin_transaction WHERE trxn_code=?");) {
            ps.setString(1, trxnCode);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    if (rs.getInt("trx_count") > 0) {
                        status = true;
                    }
                }
            } catch (SQLException e) {
                throw e;
            }
        } catch (Exception e) {
        }

        return status;
    }

}

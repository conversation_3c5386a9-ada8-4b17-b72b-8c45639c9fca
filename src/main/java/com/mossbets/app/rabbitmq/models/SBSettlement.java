package com.mossbets.app.rabbitmq.models;

import jakarta.enterprise.context.ApplicationScoped;
import java.io.Serializable;

@ApplicationScoped
public class SBSettlement implements Serializable {

    private int Status;
    private String signature;
    private String ipAddress;
    private String StatusDesc;
    private String CreatedBy;
    private String createdDate;
    private long BetId;
    private long BetDebitId;
    private long BetCreditId;
    private double Stake;
    private double Payout;

    public long getBetId() {
        return BetId;
    }

    public void setBetId(long BetId) {
        this.BetId = BetId;
    }

    public long getBetDebitId() {
        return BetDebitId;
    }

    public void setBetDebitId(long BetDebitId) {
        this.BetDebitId = BetDebitId;
    }

    public long getBetCreditId() {
        return BetCreditId;
    }

    public void setBetCreditId(long BetCreditId) {
        this.BetCreditId = BetCreditId;
    }

    public double getStake() {
        return Stake;
    }

    public void setStake(double Stake) {
        this.Stake = Stake;
    }

    public double getPayout() {
        return Payout;
    }

    public void setPayout(double Payout) {
        this.Payout = Payout;
    }

    public int getStatus() {
        return Status;
    }

    public void setStatus(int Status) {
        this.Status = Status;
    }

    public String getStatusDesc() {
        return StatusDesc;
    }

    public void setStatusDesc(String StatusDesc) {
        this.StatusDesc = StatusDesc;
    }

    public String getCreatedBy() {
        return CreatedBy;
    }

    public void setCreatedBy(String CreatedBy) {
        this.CreatedBy = CreatedBy;
    }

    public String getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(String createdDate) {
        this.createdDate = createdDate;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

}

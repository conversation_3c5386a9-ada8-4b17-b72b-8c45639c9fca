package com.mossbets.app.rabbitmq.models;

import jakarta.enterprise.context.ApplicationScoped;
import java.io.Serializable;

@ApplicationScoped
public class MpesaDeposits implements Serializable {

    private double TransAmount;
    private double OrgAccountBalance;
    private String MSISDN;
    private String Currency;
    private String FirstName;
    private String MiddleName;
    private String LastName;
    private String TransID;
    private String BusinessShortCode;
    private String TransTime;
    private String TransactionType;
    private String BillRefNumber;
    private String ThirdPartyTransID;
    private String MerchantRequestID;
    private String Signature;

    public String getMSISDN() {
        return MSISDN;
    }

    public void setMSISDN(String MSISDN) {
        this.MSISDN = MSISDN;
    }

    public double getTransAmount() {
        return TransAmount;
    }

    public void setTransAmount(double TransAmount) {
        this.TransAmount = TransAmount;
    }

    public double getOrgAccountBalance() {
        return OrgAccountBalance;
    }

    public void setOrgAccountBalance(double OrgAccountBalance) {
        this.OrgAccountBalance = OrgAccountBalance;
    }

    public String getFirstName() {
        return FirstName;
    }

    public void setFirstName(String FirstName) {
        this.FirstName = FirstName;
    }

    public String getMiddleName() {
        return MiddleName;
    }

    public void setMiddleName(String MiddleName) {
        this.MiddleName = MiddleName;
    }

    public String getLastName() {
        return LastName;
    }

    public void setLastName(String LastName) {
        this.LastName = LastName;
    }

    public String getTransID() {
        return TransID;
    }

    public void setTransID(String TransID) {
        this.TransID = TransID;
    }

    public String getBusinessShortCode() {
        return BusinessShortCode;
    }

    public void setBusinessShortCode(String BusinessShortCode) {
        this.BusinessShortCode = BusinessShortCode;
    }

    public String getTransTime() {
        return TransTime;
    }

    public void setTransTime(String TransTime) {
        this.TransTime = TransTime;
    }

    public String getTransactionType() {
        return TransactionType;
    }

    public void setTransactionType(String TransactionType) {
        this.TransactionType = TransactionType;
    }

    public String getBillRefNumber() {
        return BillRefNumber;
    }

    public void setBillRefNumber(String BillRefNumber) {
        this.BillRefNumber = BillRefNumber;
    }

    public String getThirdPartyTransID() {
        return ThirdPartyTransID;
    }

    public void setThirdPartyTransID(String ThirdPartyTransID) {
        this.ThirdPartyTransID = ThirdPartyTransID;
    }

    public String getMerchantRequestID() {
        return MerchantRequestID;
    }

    public void setMerchantRequestID(String MerchantRequestID) {
        this.MerchantRequestID = MerchantRequestID;
    }

    public String getSignature() {
        return Signature;
    }

    public void setSignature(String Signature) {
        this.Signature = Signature;
    }

    public String getCurrency() {
        return Currency;
    }

    public void setCurrency(String Currency) {
        this.Currency = Currency;
    }

}

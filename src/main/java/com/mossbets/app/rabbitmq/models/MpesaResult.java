package com.mossbets.app.rabbitmq.models;

import jakarta.enterprise.context.ApplicationScoped;
import java.io.Serializable;

@ApplicationScoped
public class MpesaResult implements Serializable {

    private long requestId;
    private int ResultType;
    private int ResultCode;
    private String ResultDesc;
    private String TransactionId;
    private String ConversationId;
    private String OriginalConversationId;
    private double amount;
    private double charges;
    private double chargesPaid;
    private double withdoldingTax;
    private double orgUtilityBalance;
    private double orgWorkingBalance;
    private String TransactionCompletedDateTime;
    private String ReceiverPartyPublicName;
    private String uniqueId;
    private String orgShortCode;
    private String initiatedDate;
    private String orgName;
    private String ipAddress;
    private String signature;

    public int getResultType() {
        return ResultType;
    }

    public void setResultType(int ResultType) {
        this.ResultType = ResultType;
    }

    public int getResultCode() {
        return ResultCode;
    }

    public void setResultCode(int ResultCode) {
        this.ResultCode = ResultCode;
    }

    public String getResultDesc() {
        return ResultDesc;
    }

    public void setResultDesc(String ResultDesc) {
        this.ResultDesc = ResultDesc;
    }

    public String getTransactionId() {
        return TransactionId;
    }

    public void setTransactionId(String TransactionId) {
        this.TransactionId = TransactionId;
    }

    public String getConversationId() {
        return ConversationId;
    }

    public void setConversationId(String ConversationId) {
        this.ConversationId = ConversationId;
    }

    public String getOriginalConversationId() {
        return OriginalConversationId;
    }

    public void setOriginalConversationId(String OriginalConversationId) {
        this.OriginalConversationId = OriginalConversationId;
    }

    public double getAmount() {
        return amount;
    }

    public void setAmount(double amount) {
        this.amount = amount;
    }

    public double getCharges() {
        return charges;
    }

    public void setCharges(double charges) {
        this.charges = charges;
    }

    public double getChargesPaid() {
        return chargesPaid;
    }

    public void setChargesPaid(double chargesPaid) {
        this.chargesPaid = chargesPaid;
    }

    public double getOrgUtilityBalance() {
        return orgUtilityBalance;
    }

    public void setOrgUtilityBalance(double orgUtilityBalance) {
        this.orgUtilityBalance = orgUtilityBalance;
    }

    public double getOrgWorkingBalance() {
        return orgWorkingBalance;
    }

    public void setOrgWorkingBalance(double orgWorkingBalance) {
        this.orgWorkingBalance = orgWorkingBalance;
    }

    public String getTransactionCompletedDateTime() {
        return TransactionCompletedDateTime;
    }

    public void setTransactionCompletedDateTime(String TransactionCompletedDateTime) {
        this.TransactionCompletedDateTime = TransactionCompletedDateTime;
    }

    public String getReceiverPartyPublicName() {
        return ReceiverPartyPublicName;
    }

    public void setReceiverPartyPublicName(String ReceiverPartyPublicName) {
        this.ReceiverPartyPublicName = ReceiverPartyPublicName;
    }

    public String getUniqueId() {
        return uniqueId;
    }

    public void setUniqueId(String uniqueId) {
        this.uniqueId = uniqueId;
    }

    public String getOrgShortCode() {
        return orgShortCode;
    }

    public void setOrgShortCode(String orgShortCode) {
        this.orgShortCode = orgShortCode;
    }

    public String getInitiatedDate() {
        return initiatedDate;
    }

    public void setInitiatedDate(String initiatedDate) {
        this.initiatedDate = initiatedDate;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    public long getRequestId() {
        return requestId;
    }

    public void setRequestId(long requestId) {
        this.requestId = requestId;
    }

    public double getWithdoldingTax() {
        return withdoldingTax;
    }

    public void setWithdoldingTax(double withdoldingTax) {
        this.withdoldingTax = withdoldingTax;
    }

}

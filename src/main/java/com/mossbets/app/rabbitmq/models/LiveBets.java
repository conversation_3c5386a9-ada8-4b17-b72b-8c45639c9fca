package com.mossbets.app.rabbitmq.models;

import jakarta.enterprise.context.ApplicationScoped;
import java.io.Serializable;
import org.codehaus.jettison.json.JSONArray;

@ApplicationScoped
public class LiveBets implements Serializable {

    private long betId;
    private long profileId;
    private double totalStake;
    private String betReference;
    private String currencyCode;
    private String priceChangeRule;
    private JSONArray betLegs;

    public long getBetId() {
        return betId;
    }

    public void setBetId(long betId) {
        this.betId = betId;
    }

    public long getProfileId() {
        return profileId;
    }

    public void setProfileId(long profileId) {
        this.profileId = profileId;
    }

    public double getTotalStake() {
        return totalStake;
    }

    public void setTotalStake(double totalStake) {
        this.totalStake = totalStake;
    }

    public String getBetReference() {
        return betReference;
    }

    public void setBetReference(String betReference) {
        this.betReference = betReference;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getPriceChangeRule() {
        return priceChangeRule;
    }

    public void setPriceChangeRule(String priceChangeRule) {
        this.priceChangeRule = priceChangeRule;
    }

    public JSONArray getBetLegs() {
        return betLegs;
    }

    public void setBetLegs(JSONArray betLegs) {
        this.betLegs = betLegs;
    }

}

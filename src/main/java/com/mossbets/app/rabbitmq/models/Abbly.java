package com.mossbets.app.rabbitmq.models;

import jakarta.enterprise.context.ApplicationScoped;
import java.io.Serializable;
import org.codehaus.jettison.json.JSONArray;

@ApplicationScoped
public class Abbly implements Serializable {

    private long timestamp;
    private long BetId;
    private boolean IsBetAllowed;
    private double maxAllowedStake;
    private String betDelay;
    private String BetAssessmentResult;
    private JSONArray BetLegs;

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public long getBetId() {
        return BetId;
    }

    public void setBetId(long BetId) {
        this.BetId = BetId;
    }

    public boolean isIsBetAllowed() {
        return IsBetAllowed;
    }

    public void setIsBetAllowed(boolean IsBetAllowed) {
        this.IsBetAllowed = IsBetAllowed;
    }

    public double getMaxAllowedStake() {
        return maxAllowedStake;
    }

    public void setMaxAllowedStake(double maxAllowedStake) {
        this.maxAllowedStake = maxAllowedStake;
    }

    public String getBetDelay() {
        return betDelay;
    }

    public void setBetDelay(String betDelay) {
        this.betDelay = betDelay;
    }

    public String getBetAssessmentResult() {
        return BetAssessmentResult;
    }

    public void setBetAssessmentResult(String BetAssessmentResult) {
        this.BetAssessmentResult = BetAssessmentResult;
    }

    public JSONArray getBetLegs() {
        return BetLegs;
    }

    public void setBetLegs(JSONArray BetLegs) {
        this.BetLegs = BetLegs;
    }
}

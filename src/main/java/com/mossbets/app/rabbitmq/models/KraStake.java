package com.mossbets.app.rabbitmq.models;

import jakarta.enterprise.context.ApplicationScoped;
import java.io.Serializable;
import org.codehaus.jettison.json.JSONObject;

@ApplicationScoped
public class KraStake implements Serializable {

    private double betAmount;
    private double betAmountAfterTax;
    private long timestamp;
    private long customerId;
    private long betId;
    private String betReference;
    private String betType;
    private String mobileNumber;
    private JSONObject betData;

    public double getBetAmount() {
        return betAmount;
    }

    public void setBetAmount(double betAmount) {
        this.betAmount = betAmount;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(long customerId) {
        this.customerId = customerId;
    }

    public String getBetType() {
        return betType;
    }

    public void setBetType(String betType) {
        this.betType = betType;
    }

    public String getMobileNumber() {
        return mobileNumber;
    }

    public void setMobileNumber(String mobileNumber) {
        this.mobileNumber = mobileNumber;
    }

    public JSONObject getBetData() {
        return betData;
    }

    public void setBetData(JSONObject betData) {
        this.betData = betData;
    }

    public long getBetId() {
        return betId;
    }

    public void setBetId(long betId) {
        this.betId = betId;
    }

    public String getBetReference() {
        return betReference;
    }

    public void setBetReference(String betReference) {
        this.betReference = betReference;
    }

    public double getBetAmountAfterTax() {
        return betAmountAfterTax;
    }

    public void setBetAmountAfterTax(double betAmountAfterTax) {
        this.betAmountAfterTax = betAmountAfterTax;
    }
}

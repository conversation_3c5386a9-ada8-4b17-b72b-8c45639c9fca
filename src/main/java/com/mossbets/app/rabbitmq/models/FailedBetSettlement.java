package com.mossbets.app.rabbitmq.models;

import jakarta.enterprise.context.ApplicationScoped;
import java.io.Serializable;

@ApplicationScoped
public class FailedBetSettlement implements Serializable {

    private long BetId;
    private String Signature;

    public long getBetId() {
        return BetId;
    }

    public void setBetId(long BetId) {
        this.BetId = BetId;
    }

    public String getSignature() {
        return Signature;
    }

    public void setSignature(String Signature) {
        this.Signature = Signature;
    }
}

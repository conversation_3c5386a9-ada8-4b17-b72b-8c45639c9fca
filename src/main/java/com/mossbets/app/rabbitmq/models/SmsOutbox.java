package com.mossbets.app.rabbitmq.models;

import jakarta.enterprise.context.ApplicationScoped;
import java.io.Serializable;

@ApplicationScoped
public class SmsOutbox implements Serializable {

    private int gateway;
    private int smsPages;

    private String dlrUrl;
    private String message;
    private String network;
    private String smsId;
    private String smsLinkId;
    private String outboxId;
    private String authToken;
    private String alertType;
    private String shortCode;
    private String recipients;
    private String campaignId;
    private String networkRegex;

    public int getGateway() {
        return gateway;
    }

    public void setGateway(int gateway) {
        this.gateway = gateway;
    }

    public int getSmsPages() {
        return smsPages;
    }

    public void setSmsPages(int smsPages) {
        this.smsPages = smsPages;
    }

    public String getNetworkRegex() {
        return networkRegex;
    }

    public void setNetworkRegex(String networkRegex) {
        this.networkRegex = networkRegex;
    }

    public String getDlrUrl() {
        return dlrUrl;
    }

    public void setDlrUrl(String dlrUrl) {
        this.dlrUrl = dlrUrl;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getOutboxId() {
        return outboxId;
    }

    public void setOutboxId(String outboxId) {
        this.outboxId = outboxId;
    }

    public String getAuthToken() {
        return authToken;
    }

    public void setAuthToken(String authToken) {
        this.authToken = authToken;
    }

    public String getAlertType() {
        return alertType;
    }

    public void setAlertType(String alertType) {
        this.alertType = alertType;
    }

    public String getShortCode() {
        return shortCode;
    }

    public void setShortCode(String shortCode) {
        this.shortCode = shortCode;
    }

    public String getRecipients() {
        return recipients;
    }

    public void setRecipients(String recipients) {
        this.recipients = recipients;
    }

    public String getCampaignId() {
        return campaignId;
    }

    public void setCampaignId(String campaignId) {
        this.campaignId = campaignId;
    }

    public String getSmsId() {
        return smsId;
    }

    public void setSmsId(String smsId) {
        this.smsId = smsId;
    }

    public String getNetwork() {
        return network;
    }

    public void setNetwork(String network) {
        this.network = network;
    }

    public String getSmsLinkId() {
        return smsLinkId;
    }

    public void setSmsLinkId(String smsLinkId) {
        this.smsLinkId = smsLinkId;
    }

}

package com.mossbets.app.rabbitmq.models;

import jakarta.enterprise.context.ApplicationScoped;
import java.io.Serializable;

@ApplicationScoped
public class MpesaWithdrawal implements Serializable {

    private long trxnId;
    private long profileId;
    private long paybillId;
    private double amount;
    private String ip;
    private String date;
    private String appKey;
    private String currency;
    private String channel;
    private String signature;
    private String ipAddress;
    private String phoneNumber;
    private String callBackURL;
    private String paybillNumber;

    public long getTrxnId() {
        return trxnId;
    }

    public void setTrxnId(long trxnId) {
        this.trxnId = trxnId;
    }

    public double getAmount() {
        return amount;
    }

    public void setAmount(double amount) {
        this.amount = amount;
    }

    public String getPaybillNumber() {
        return paybillNumber;
    }

    public void setPaybillNumber(String paybillNumber) {
        this.paybillNumber = paybillNumber;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getCallBackURL() {
        return callBackURL;
    }

    public void setCallBackURL(String callBackURL) {
        this.callBackURL = callBackURL;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public long getPaybillId() {
        return paybillId;
    }

    public void setPaybillId(long paybillId) {
        this.paybillId = paybillId;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public long getProfileId() {
        return profileId;
    }

    public void setProfileId(long profileId) {
        this.profileId = profileId;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }
}

package com.mossbets.app.rabbitmq.consumers;

import com.mossbets.app.rabbitmq.models.FailedBetSettlement;
import com.mossbets.app.rabbitmq.models.SBSettlement;
import com.mossbets.app.rabbitmq.utils.RabbitUtils;
import com.mossbets.app.resources.DB;
import com.mossbets.app.utils.ApiCalls;
import com.mossbets.app.utils.MailUtils;
import com.mossbets.app.utils.Queue;
import com.mossbets.app.utils.Utilities;
import static com.mossbets.app.utils.Utilities.GetBFRAuthToken;
import com.mossbets.app.utils.props.Basics;
import com.mossbets.app.utils.props.Props;
import com.rabbitmq.client.*;
import io.quarkus.runtime.ShutdownEvent;
import io.quarkus.runtime.StartupEvent;
import io.quarkus.scheduler.Scheduled;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.event.Observes;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.SQLIntegrityConstraintViolationException;
import java.time.Instant;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import org.codehaus.jettison.json.JSONArray;
import org.codehaus.jettison.json.JSONObject;
import org.jboss.logging.Logger;

@ApplicationScoped
public class BetSettlement {

    @Inject
    Props props;

    @Inject
    Basics basics = new Basics();

    @Inject
    DB db;

    @Inject
    Logger logger;

    @Inject
    private Queue Queue;

    @Inject
    private MailUtils MailUtils;

    @Inject
    @Named("queueExecutor")
    ExecutorService es;

    @Inject
    ScheduledExecutorService scheduler;

    private Connection connection;
    private int reconnectAttempt = 0;
    private final int maxBackoffSeconds = 60;
    private volatile boolean running = true;

    void onStart(@Observes StartupEvent ev) {
        logger.info(Utilities.getLogPreString("BetSettlement")
                + "onStart()"
                + "|Starting RabbitMQ Consumer...");
        scheduleReconnect(0);
    }

    void onStop(@Observes ShutdownEvent event) {
        logger.info(Utilities.getLogPreString("BetSettlement")
                + "onStop()"
                + "|Application shutting down...");
        cleanup();
    }

    @PostConstruct
    void startOnAppBoot() {
        scheduleReconnect(0); // ✅ triggers startup
    }

    /**
     * scheduleReconnect
     *
     * @param delaySeconds
     */
    private void scheduleReconnect(int delaySeconds) {
        scheduler.schedule(this::connectWithBackoff, delaySeconds, TimeUnit.SECONDS);
    }

    /**
     * connectWithBackoff
     */
    private void connectWithBackoff() {
        if (!running) {
            return;
        }

        try {
            connectAndStartConsuming();
            logger.info(Utilities.getLogPreString("BetSettlement")
                    + "connectWithBackoff()"
                    + "|RabbitMQ Consumer connected and consuming....");
            reconnectAttempt = 0; // reset on success
        } catch (Exception e) {
            reconnectAttempt++;
            int backoff = calculateBackoff(reconnectAttempt);
            logger.errorf(Utilities.getLogPreString("BetSettlement")
                    + "connectWithBackoff()"
                    + "|Reconnect failed (attempt #%d). Retrying in %ds...",
                    reconnectAttempt, backoff);
            scheduleReconnect(backoff);
        }
    }

    /**
     * calculateBackoff
     *
     * @param attempt
     * @return
     */
    private int calculateBackoff(int attempt) {
        int base = (int) Math.min(Math.pow(2, attempt), maxBackoffSeconds);
        int jitter = ThreadLocalRandom.current().nextInt(0, 5);
        return base + jitter; // Exponential + jitter
    }

    /**
     * createFactory
     *
     * @return
     */
    private ConnectionFactory createFactory() {
        ConnectionFactory factory = new ConnectionFactory();
        factory.setHost(props.rabbitMqHost());
        factory.setPort(props.rabbitMqPort());
        factory.setVirtualHost(props.rabbitMqVhost());
        factory.setUsername(props.rabbitMqUsername());
        factory.setPassword(props.rabbitMqPassword());
        factory.setTopologyRecoveryEnabled(props.rabbitMqConRecovery());
        factory.setAutomaticRecoveryEnabled(props.rabbitMqTopologyRecovery());
        factory.setNetworkRecoveryInterval(props.rabbitMqNetworkRecovery());
        return factory;
    }

    /**
     * connectAndStartConsuming
     *
     * @throws Exception
     */
    private void connectAndStartConsuming() throws Exception {
        connection = createFactory().newConnection();
        connection.addShutdownListener(cause -> {
            logger.info(Utilities.getLogPreString("BetSettlement")
                    + "connectAndStartConsuming()"
                    + "|Connection shutdown: " + cause.getMessage());
            if (running) {
                scheduleReconnect(0); // retry again
            }
        });

        for (String queue : props.BetSettlementQueues()) {
            String queueName = props.rabbitMqPrefix().concat("_" + queue + "_QUEUE");
            String exchange = props.rabbitMqPrefix().concat("_" + queue + "_EXCHANGE");
            String route = props.rabbitMqPrefix().concat("_" + queue + "_ROUTE");

            Channel channel = connection.createChannel();
            channel.queueDeclare(queueName, true, false, false, null);
            channel.exchangeDeclare(exchange, "direct", true, false, null);
            channel.queueBind(queueName, exchange, route);
            channel.basicQos(props.rabbitMqPrefetchCount());
            channel.basicConsume(queueName, false, new DefaultConsumer(channel) {
                @Override
                @SuppressWarnings("UseSpecificCatch")
                public void handleDelivery(String consumerTag, Envelope envelope,
                        AMQP.BasicProperties properties, byte[] body) {
                    long deliveryTag = envelope.getDeliveryTag();
                    try {
                        es.submit(() -> handleMessage(body, queueName, channel, deliveryTag));
                    } catch (Exception ex) {
                        logger.error(Utilities.getLogPreString("BetSettlement")
                                + "connectAndStartConsuming()"
                                + "|Queue " + queueName
                                + "|ConsumerTag:" + consumerTag
                                + "|Exception: " + ex.getMessage());
                        rejectAck(channel, deliveryTag, true);
                    }
                }
            });

            logger.infof(Utilities.getLogPreString("BetSettlement")
                    + "connectAndStartConsuming()"
                    + "|Queue [%s]"
                    + "|Exchange [%s]"
                    + "|Route [%s]", queueName, exchange, route);
        }
    }

    /**
     * handleMessage
     *
     * @param body
     * @param queueName
     * @param channel
     * @param deliveryTag
     */
    @SuppressWarnings("UseSpecificCatch")
    private void handleMessage(byte[] body, String queueName, Channel channel, long deliveryTag) {
        try {
            if (queueName.contains("LOST_BET_SETTLEMENT")) {
                if (handleLostBetSettlement(body, queueName)) {
                    successAck(channel, deliveryTag);
                } else {
                    rejectAck(channel, deliveryTag, true);
                }
                return;
            }

            if (handleBetSettlement(body, queueName)) {
                successAck(channel, deliveryTag);
            } else {
                rejectAck(channel, deliveryTag, true);
            }
        } catch (Exception e) {
            rejectAck(channel, deliveryTag, true);

            logger.error(Utilities.getLogPreString("BetSettlement")
                    + "handleMessage()"
                    + "-" + queueName
                    + "|Exception:" + e.getMessage());
        }
    }

    /**
     * handleLostBetSettlement
     *
     * @param body
     * @param queueName
     * @return
     */
    @SuppressWarnings("UseSpecificCatch")
    private boolean handleLostBetSettlement(byte[] body, String queueName) {
        Instant tat = Instant.now();
        boolean processed = false;
        String message = new String(body, StandardCharsets.UTF_8);
        try {
            logger.info(Utilities.getLogPreString("BetSettlement")
                    + "handleLostBetSettlement()"
                    + "|Received:" + queueName
                    + "|Msg:" + message);

            FailedBetSettlement bet = new FailedBetSettlement();
            try {
                JSONObject obj = new JSONObject(message);
                bet.setBetId(obj.getLong("betId"));
            } catch (Exception js) {
                logger.error(Utilities.getLogPreString("BetSettlement")
                        + "handleLostBetSettlement()"
                        + "-" + queueName
                        + "|Took " + Utilities.CalculateTAT(tat) + " mSec(s)"
                        + "|Exception:" + js.getMessage());
                return true;
            }

            Map<String, Object> betInfo = null;
            try (final java.sql.Connection dbConn = db.ReadDataSource("bets").getConnection();) {
                betInfo = RabbitUtils.getSportsBetDetails(bet.getBetId(), dbConn);
            } catch (Exception e) {
            }

            if (null == betInfo) {
                logger.info(Utilities.getLogPreString("BetSettlement")
                        + "handleLostBetSettlement()"
                        + "|BetId:" + bet.getBetId()
                        + "|Bet NOT FOUND!!!");
                return true;
            }

            if ((int) betInfo.get("status") != 3) {
                logger.info(Utilities.getLogPreString("BetSettlement")
                        + "handleLostBetSettlement()"
                        + "|BetId:" + bet.getBetId()
                        + "|Bet Already processed!!!");
                return true;
            }

            logger.info(Utilities.getLogPreString("BetSettlement")
                    + "handleLostBetSettlement()"
                    + "|BetId:" + bet.getBetId()
                    + "|Lost Bet!!!");

            try {
                JSONObject eData = new JSONObject(betInfo.get("eData").toString());
                if (eData.has("genius_bfr")) {

                    JSONObject geniusBFR = eData.getJSONObject("genius_bfr");
                    if (geniusBFR.getBoolean("posted")) {
                        String authToken = GetBFRAuthToken();
                        if (!Utilities.isBlank(authToken)) {
                            Map<String, String> httpHeader = new HashMap<>();
                            httpHeader.put("User-Agent", basics.UA);
                            httpHeader.put("X-Organization-Id", "Mossbets/v1");
                            httpHeader.put("Authorization", "Bearer " + authToken);
                            httpHeader.put("x-api-key", props.geniusXApiKey());

                            JSONArray legs = new JSONArray();
                            try (final java.sql.Connection betDb
                                    = db.ReadDataSource("bets").getConnection();
                                    PreparedStatement ps = betDb.prepareStatement(
                                            "SELECT IF(live_bet=0,'PreMatch','InPlay') AS game_state"
                                            + ",odd_value as odds,selection_id,status "
                                            + "FROM sports_bet_slip WHERE bet_id=?");) {
                                ps.setLong(1, bet.getBetId());
                                try (ResultSet rs = ps.executeQuery()) {
                                    while (rs.next()) {
                                        String status = "Open";
                                        double payoutPrice = 0.00;
                                        if (Utilities.FindInIntegerArray(new int[]{1, 3, 9},
                                                rs.getInt("status"))) {
                                            status = "Settled";
                                            payoutPrice = Utilities.Round2Decimal(
                                                    rs.getDouble("odds"));
                                            if (rs.getInt("status") == 3) {
                                                payoutPrice = 0;
                                            }
                                        }

                                        if (rs.getInt("status") == 7) {
                                            status = "Cancelled";
                                            payoutPrice = Utilities.Round2Decimal(
                                                    rs.getDouble("odds"));
                                        }

                                        legs.put(new JSONObject()
                                                .put("price", Utilities
                                                        .Round2Decimal(rs.getDouble("odds")))
                                                .put("gameState", rs.getString("game_state"))
                                                .put("status", status)
                                                .put("payoutPrice", payoutPrice)
                                                .put("betgeniusContent", new JSONObject()
                                                        .put("selectionId",
                                                                rs.getString("selection_id"))));
                                    }
                                } catch (SQLException s) {
                                }
                            } catch (SQLException s) {
                            }

                            if (legs.length() > 0) {

                                Instant betPlacedTimestampUTC = Instant.now();
                                if (geniusBFR.has("betTime")) {
                                    betPlacedTimestampUTC = (Instant) geniusBFR.get("betTime");
                                }

                                Instant httpTime = Instant.now();
                                Map<String, Object> httpResponse
                                        = ApiCalls.sendHttpJsonPostData(props.geniusRiskBfrUrl(),
                                                new JSONObject()
                                                        .put("id", String.valueOf(bet.getBetId()))
                                                        .put("betPlacedTimestampUTC", betPlacedTimestampUTC)
                                                        .put("betUpdatedTimestampUTC", betPlacedTimestampUTC)
                                                        .put("bookmakerName", "mossbets")
                                                        .put("payout", 0.00)
                                                        .put("playerId", betInfo
                                                                .get("profileId").toString())
                                                        .put("totalStake", ((double) betInfo.get("stake")
                                                                - (double) betInfo.get("exciseTax")))
                                                        .put("currencyCode", betInfo.get("currency"))
                                                        .put("priority", 1)
                                                        .put("status", "Settled")
                                                        .put("legs", legs).toString(),
                                                httpHeader,
                                                basics,
                                                logger, false);
                                logger.info(Utilities.getLogPreString("BetSettlement")
                                        + "handleLostBetSettlement()"
                                        + "|BetId:" + bet.getBetId()
                                        + "|BetReference:" + betInfo.get("betReference")
                                        + "|BetStatus: " + betInfo.get("status")
                                        + "|pId: " + betInfo.get("profileId") + "-" + eData.getString("msisdn")
                                        + "|BFR Settlement Results =>" + httpResponse.toString());

                                geniusBFR.put("resulted", false);
                                if ((int) httpResponse.get("statusCode") == 200) {
                                    geniusBFR.put("resulted", true);
                                }

                                eData.put("genius_bfr", geniusBFR);
                                RabbitUtils.updateStatements(db.WriteDataSource("bets"),
                                        "UPDATE sports_bet SET extra_data=? WHERE bet_id=? LIMIT 1",
                                        new Object[]{eData.toString(), bet.getBetId()});
                            }
                        }
                    }
                }
            } catch (Exception e) {
            }

            processed = true;
        } catch (Exception e) {
            logger.error(Utilities.getLogPreString("BetSettlement")
                    + "handleLostBetSettlement()"
                    + "-" + queueName
                    + "|Took " + Utilities.CalculateTAT(tat) + " mSec(s)"
                    + "|Exception:", e);
            processed = false;
        }

        return processed;
    }

    /**
     * handleBetSettlement
     *
     * @param body
     * @param queueName
     * @return
     */
    @SuppressWarnings("UseSpecificCatch")
    private boolean handleBetSettlement(byte[] body, String queueName) {
        Instant tat = Instant.now();
        boolean processed = false;
        String message = new String(body, StandardCharsets.UTF_8);
        try {
            logger.info(Utilities.getLogPreString("BetSettlement")
                    + "handleBetSettlement()"
                    + "|Received"
                    + "-" + queueName
                    + "- Msg:" + message);

            SBSettlement bet = new SBSettlement();
            try {
                JSONObject obj = new JSONObject(message);
                bet.setBetId(obj.getLong("betId"));
                bet.setStatus(obj.getInt("status"));
                bet.setSignature(obj.getString("signature"));
                bet.setIpAddress(obj.getString("ipAddress"));
                bet.setCreatedBy(obj.getString("createdBy"));
                bet.setStatusDesc(obj.getString("statusDesc"));
                bet.setCreatedDate(obj.getString("createdDate"));
            } catch (Exception js) {
                logger.error(Utilities.getLogPreString("BetSettlement")
                        + "handleBetSettlement()"
                        + "-" + queueName
                        + "|Took " + Utilities.CalculateTAT(tat) + " mSec(s)"
                        + "|Exception:" + js.getMessage());
                return true;
            }

            String provider = "GENIUS SPORTS";

            Map<String, Object> betInfo = null;
            Map<String, Double> betLimits = null;
            Map<String, Object> betSlipInfo = null;
            try (final java.sql.Connection dbConn = db.ReadDataSource("bets").getConnection();) {
                betInfo = RabbitUtils.getSportsBetDetails(bet.getBetId(), dbConn);
                if (null != betInfo) {
                    betSlipInfo = RabbitUtils.getSportsBetSlipDetails(bet.getBetId(), dbConn);
                    betLimits = RabbitUtils.getBetLimits((int) betInfo.get("clientId"),
                            betInfo.get("currency").toString(), provider, dbConn);
                }
            } catch (Exception e) {
            }

            if ((null == betInfo) || (betSlipInfo == null)) {
                logger.info(Utilities.getLogPreString("BetSettlement")
                        + "handleBetSettlement()"
                        + "|BetId:" + bet.getBetId()
                        + "|Bet NOT FOUND!!!");
                return true;
            }

            if (null == betLimits) {
                logger.info(Utilities.getLogPreString("BetSettlement")
                        + "handleBetSettlement()"
                        + "|BetId:" + bet.getBetId()
                        + "|Kindly Set up the " + provider + " Bet Limits");
                return false;
            }

            if (Utilities.FindInIntegerArray(basics.finalBetStatus,
                    (int) betInfo.get("status"))) {
                if ((long) betInfo.get("creditId") > 0) {
                    logger.info(Utilities.getLogPreString("BetSettlement")
                            + "handleBetSettlement()"
                            + "|BetId:" + bet.getBetId()
                            + "|Bet Already processed!!!");
                    return true;
                }
            }

            String signature = generateSignature(bet.getIpAddress());
            if (!bet.getSignature().equalsIgnoreCase(signature)) {
                logger.warn(Utilities.getLogPreString("BetSettlement")
                        + "handleBetSettlement()"
                        + "|BetId:" + bet.getBetId()
                        + "|Invalid Signature");

                String emailSubject = "Bet Settlement Risk Issue";

                Set<String> emailList = new LinkedHashSet<>();
                emailList.add("<EMAIL>");
                emailList.add("<EMAIL>");
                emailList.add("<EMAIL>");
                emailList.add("<EMAIL>");
                emailList.add("<EMAIL>");

                MailUtils.SendMail(
                        String.join(",", emailList),
                        "Risk team",
                        new StringBuilder()
                                .append("<h4>Risk Approvals</h4>")
                                .append("<span>ID:</span>")
                                .append(bet.getBetId())
                                .append("<span>Games:</span>")
                                .append(betInfo.get("totalGames"))
                                .append("<br/><span>Status:</span>")
                                .append(bet.getStatus())
                                .append("<br/><span>TransactionId:</span>")
                                .append(betInfo.get("debitId"))
                                .append("<br/><span>Amount:</span>")
                                .append(betInfo.get("currency"))
                                .append(".")
                                .append(betInfo.get("stake"))
                                .append("<br/><span>Date:</span>")
                                .append(betInfo.get("createdAt")).toString(),
                        emailSubject, null);

                return true;
            }

            bet.setStatus((int) betInfo.get("status"));
            if (!Utilities.FindInIntegerArray(new int[]{1, 7}, bet.getStatus())) {
                logger.info(Utilities.getLogPreString("BetSettlement")
                        + "handleBetSettlement()"
                        + "|BetId:" + bet.getBetId()
                        + "|Betslip(s) is neither WON/VOIDED"
                        + "|Current SlipState:" + bet.getStatus());
                return true;
            }

            if ((int) betSlipInfo.get("count") != (int) betInfo.get("totalGames")) {
                logger.info(Utilities.getLogPreString("BetSettlement")
                        + "handleBetSettlement()"
                        + "|BetId:" + bet.getBetId()
                        + "|Betslip(s) count dont match with the total games"
                        + "|TotalGames:" + betInfo.get("totalGames")
                        + "|BetSlips:" + betInfo.get("count"));
                return true;
            }

            boolean kraReport = false;
            if ((int) betInfo.get("kraReport") == 1) {
                kraReport = true;
            }

            int transTypeId = 1;//CREDIT
            int referenceTypeId = 5;//SPORTS_BOOK_BET_WINS

            String source = "SPORTS_BOOK_BET_WINS";
            String transDesc = "(SportBook) WON Bet. BetId:" + betInfo.get("betReference");
            JSONObject eData = new JSONObject(betInfo.get("eData").toString());

            JSONObject trxnData = new JSONObject()
                    .put("ip", bet.getIpAddress())
                    .put("total_events", (int) betInfo.get("totalGames"))
                    .put("bet_id", bet.getBetId())
                    .put("status", bet.getStatus());

            double stake = (double) betInfo.get("stake");
            double payout = (double) betInfo.get("payout");
            double odds = Utilities.Round2Decimal((double) betInfo.get("odds"));
            double betSlipOddsInfo = Utilities.Round2Decimal((double) betSlipInfo.get("odds"));

            if (bet.getStatus() == 7 || (betSlipOddsInfo != odds)) {
                trxnData.put("old_payout", payout);
                double stakeAfterTax = ((double) betInfo.get("stake")
                        - (double) betInfo.get("exciseTax"));
                payout = betSlipOddsInfo * stakeAfterTax;

                if (bet.getStatus() == 7) {
                    referenceTypeId = 6;//SPORTS_BOOK_BET_VOID
                    source = "SPORTS_BOOK_BET_VOID";
                    payout = ((double) betInfo.get("stake") - (double) betInfo.get("exciseTax"));
                    transDesc = "(SportBook) VOID Bet. BetId:" + betInfo.get("betReference");
                }

                if (payout > betLimits.get("maxWin")) {
                    payout = betLimits.get("maxWin");
                }

                Map<String, Double> wTax = Utilities.CalculateWitholdingTax(
                        payout, stakeAfterTax, 20.00, "");

                payout = wTax.get("posibleWin");
                betInfo.put("wTax", wTax.get("witholdingTax"));

                logger.info(Utilities.getLogPreString("BetSettlement")
                        + "handleBetSettlement()"
                        + "|BetId:" + bet.getBetId()
                        + "|Mobile:" + eData.getString("msisdn")
                        + "|BetStatus:" + bet.getStatus()
                        + "|payout:" + payout
                        + "|source:" + source
                        + "|betSlipOdds:" + betSlipOddsInfo
                        + "|TotalOdds:" + odds);
            }

            String messageOut = "Congratulations!!"
                    + "\n-\n"
                    + "WON BetId: " + betInfo.get("betReference")
                    + "\nPayout " + betInfo.get("currency") + ":" + payout
                    + "\nDate " + Utilities.now("yyyy-MM-dd HH:mm:ss")
                    + "\n-\n"
                    + "Continue playing: https://mossbets.com"
                    + "\nHelp:" + basics.HelpLine;

            long outboxId = 0;
            long approvalId = 0;
            try (final java.sql.Connection betDb = db.WriteDataSource("bets").getConnection();
                    final java.sql.Connection tDb = db.WriteDataSource("trxn").getConnection();) {

                tDb.setAutoCommit(false);
                betDb.setAutoCommit(false);

                long betCreditId = 0;
                try (final PreparedStatement ps = tDb.prepareStatement(
                        "INSERT INTO transaction(profile_id,reference_type_id,transaction_type_id"
                        + ",reference_id,amount,currency,source,description"
                        + ",extra_data,created_at) VALUES(?,?,?,?,?,?,?,?,?,NOW())",
                        PreparedStatement.RETURN_GENERATED_KEYS);) {
                    ps.setLong(1, (long) betInfo.get("profileId"));
                    ps.setInt(2, referenceTypeId);
                    ps.setInt(3, transTypeId);
                    ps.setLong(4, (long) betInfo.get("debitId"));
                    ps.setDouble(5, payout);
                    ps.setString(6, betInfo.get("currency").toString());
                    ps.setString(7, source);
                    ps.setString(8, transDesc);
                    ps.setString(9, trxnData.toString());
                    betCreditId = ps.executeUpdate();
                    try (ResultSet rs = ps.getGeneratedKeys()) {
                        if (rs.next()) {
                            betCreditId = rs.getLong(1);
                        }
                    }

                    if (betCreditId < 1) {
                        tDb.rollback();
                        throw new SQLException("Failed to create refund "
                                + "transactions:" + bet.getBetId());
                    }

                    if ((payout - stake) >= betLimits.get("riskAmount")) {
                        logger.info(Utilities.getLogPreString("RefundsApp")
                                + "processRefund()"
                                + "|BetId:" + bet.getBetId()
                                + "| " + betInfo.get("currency") + ":" + payout
                                + "|Will create a Transaction that will require approvals");

                        try (final PreparedStatement psAp = tDb.prepareStatement(
                                "INSERT INTO transaction_pending_approval("
                                + "profile_id,transaction_id,amount,currency"
                                + ",source,description,created_at) VALUES(?,?,?,?,?,?,NOW())",
                                PreparedStatement.RETURN_GENERATED_KEYS);) {
                            psAp.setLong(1, (long) betInfo.get("profileId"));
                            psAp.setLong(2, betCreditId);
                            psAp.setDouble(3, payout);
                            psAp.setString(4, betInfo.get("currency").toString());
                            psAp.setString(5, source);
                            psAp.setString(6, "SportsBook Winnings Above KES."
                                    + betLimits.get("riskAmount"));
                            approvalId = psAp.executeUpdate();
                            try (ResultSet rs = psAp.getGeneratedKeys()) {
                                if (rs.next()) {
                                    approvalId = rs.getLong(1);
                                }
                            }

                            if (approvalId < 1) {
                                tDb.rollback();
                                throw new SQLException("Failed to create refund "
                                        + "approval:" + bet.getBetId());
                            }
                        } catch (SQLException e) {
                            throw e;
                        }
                    }
                } catch (SQLException e) {
                    tDb.rollback();
                    throw e;
                }

                String updateBetSql = "UPDATE sports_bet SET bet_credit_transaction_id=? "
                        + "WHERE bet_id=? LIMIT 1";
                if ((betSlipOddsInfo != odds) || bet.getStatus() == 7) {
                    updateBetSql = "UPDATE sports_bet SET bet_credit_transaction_id=?"
                            + ",total_odd=?,possible_win=?,witholding_tax=? WHERE bet_id=? "
                            + "LIMIT 1";
                }

                try (final PreparedStatement ps = betDb.prepareStatement(updateBetSql)) {
                    if ((betSlipOddsInfo != odds) || bet.getStatus() == 7) {
                        ps.setLong(1, betCreditId);
                        ps.setDouble(2, betSlipOddsInfo);
                        ps.setDouble(3, payout);
                        ps.setDouble(4, (double) betInfo.get("wTax"));
                        ps.setDouble(5, bet.getBetId());
                    } else {
                        ps.setLong(1, betCreditId);
                        ps.setLong(2, bet.getBetId());
                    }

                    int updateState = ps.executeUpdate();
                    if (updateState < 1) {
                        throw new SQLException("Failed to update BetId:" + bet.getBetId());
                    }
                } catch (SQLException e) {
                    tDb.rollback();
                    betDb.rollback();
                    throw e;
                }

                try (final java.sql.Connection pDb = db
                        .WriteDataSource("profile").getConnection();) {
                    pDb.setAutoCommit(false);
                    double bonusAmount = 0;
                    if (approvalId < 1) {
                        try (final PreparedStatement ps = pDb.prepareStatement(
                                "UPDATE profile_balance SET balance=balance+?"
                                + ",bonus=bonus+? WHERE profile_id=? LIMIT 1")) {
                            ps.setDouble(1, payout);
                            ps.setDouble(2, bonusAmount);
                            ps.setLong(3, (long) betInfo.get("profileId"));
                            int updateState = ps.executeUpdate();

                            if (updateState < 1) {
                                throw new SQLException("Failed to update Wallet "
                                        + "for BetId:" + bet.getBetId());
                            }
                        } catch (SQLException e) {
                            pDb.rollback();
                            throw e;
                        }

                        try (final PreparedStatement psO = pDb.prepareStatement(
                                "INSERT INTO profile_outbox(profile_id,sender_id,message_type"
                                + ",message,status,created_at) VALUES(?,?,?,?,?,NOW())",
                                PreparedStatement.RETURN_GENERATED_KEYS);) {
                            psO.setLong(1, (long) betInfo.get("profileId"));
                            psO.setString(2, basics.SenderId);
                            psO.setString(3, "WINNING");
                            psO.setString(4, messageOut);
                            psO.setInt(5, 1);
                            outboxId = psO.executeUpdate();
                            try (ResultSet rs = psO.getGeneratedKeys()) {
                                if (rs.next()) {
                                    outboxId = rs.getLong(1);
                                }
                            }

                            if (outboxId < 1) {
                                throw new SQLException("BetSettlement Sms Outbox for "
                                        + "profileId:" + (long) betInfo.get("profileId"));
                            }
                        } catch (SQLException e) {
                            pDb.rollback();
                            throw e;
                        }
                    }

                    try (final PreparedStatement ps = pDb.prepareStatement(
                            "UPDATE profile_attribution SET last_winning_date=NOW()"
                            + ",total_winnings=total_winnings+? WHERE profile_id=? LIMIT 1")) {
                        ps.setDouble(1, payout);
                        ps.setLong(2, (long) betInfo.get("profileId"));
                        int updateState = ps.executeUpdate();
                        if (updateState < 1) {
                            throw new SQLException("Failed to update Customer "
                                    + "Attribute for BetId:" + bet.getBetId());
                        }
                    } catch (SQLException e) {
                        pDb.rollback();
                        throw e;
                    }

                } catch (Exception e) {
                    tDb.rollback();
                    betDb.rollback();
                    throw e;
                }

                betDb.commit();
                tDb.commit();

                logger.info(Utilities.getLogPreString("BetSettlement")
                        + "handleBetSettlement()"
                        + "|BetId:" + bet.getBetId()
                        + "|Mobile:" + eData.getString("msisdn")
                        + "|BetStatus: " + bet.getStatus()
                        + "|pId: " + betInfo.get("profileId") + "-" + eData.getString("msisdn")
                        + "|Stake: " + stake
                        + "|Amount: " + betInfo.get("currency") + "." + payout);

                processed = true;
            } catch (SQLIntegrityConstraintViolationException e) {
                // This is thrown for duplicate‑entry / unique constraint violations
                // For MySQL, vendor code 1062 indicates duplicate entry
                if (e.getErrorCode() == 1062 || e.getSQLState().startsWith("23")) {
                    logger.error(Utilities.getLogPreString("BetSettlement")
                            + "handleBetSettlement()"
                            + "|BetId:" + bet.getBetId()
                            + "|Mobile:" + eData.getString("msisdn")
                            + "|pId: " + betInfo.get("profileId") + "-" + eData.getString("msisdn")
                            + "|Duplicate‑entry / unique constraint violations"
                            + "|Exception:" + e.getMessage());
                    return true;
                }

                throw e;
            } catch (Exception e) {
                throw e;
            }

            if (approvalId < 1) {
                try {
                    String smsQ = Utilities.getRandomValue(props.SmsOutboxQueue());
                    Queue.publishMessage(
                            new JSONObject()
                                    .put("campaign_id", outboxId)
                                    .put("message_pages", 1)
                                    .put("message", messageOut)
                                    .put("sms-id", "QUICKSENDVERIFICATION")
                                    .put("network_regex", 1)
                                    .put("network", "SAFARICOM")
                                    .put("alert_type", "TRANSACTIONAL")
                                    .put("recipients", eData.getString("msisdn"))
                                    .put("outbox_id", outboxId)
                                    .put("short_code", basics.SenderId)
                                    .put("gateway", 1)
                                    .put("dlr_url", "")
                                    .put("auth_token", "auth_token_api")
                                    .put("date_created", Utilities
                                            .now("yyyy-MM-dd HH:mm:ss"))
                                    .toString(),
                            smsQ,
                            smsQ,
                            smsQ,
                            null,
                            null);
                } catch (Exception e) {
                }
            }

            if (kraReport) {
                String qN = "KRA_RESULTS";
                int rand = Utilities.GetRandomInt(0, 1);
                if (rand > 0) {
                    qN += "_" + rand;
                }

                if (Queue.publishMessage(
                        new JSONObject()
                                .put("betId", bet.getBetId())
                                .put("betReference", betInfo.get("betReference"))
                                .put("betType", "SPORTS")
                                .put("stake", stake)
                                .put("payout", payout)
                                .put("date", Utilities.now("yyyy-MM-dd HH:mm:ss"))
                                .toString(),
                        qN,
                        qN,
                        qN,
                        null,
                        null)) {
                    logger.info(Utilities.getLogPreString("BetSettlement")
                            + "handleBetSettlement()"
                            + "|BetId:" + bet.getBetId()
                            + "|BetReference:" + betInfo.get("betReference")
                            + "|BetStatus: " + bet.getStatus()
                            + "|pId: " + betInfo.get("profileId") + "-" + eData.getString("msisdn")
                            + "|Stake: " + stake
                            + "|Amount: " + betInfo.get("currency") + "." + payout);
                }
            }

            try {
                if (eData.has("genius_bfr")) {
                    JSONObject geniusBFR = eData.getJSONObject("genius_bfr");
                    if (geniusBFR.getBoolean("posted")) {
                        String authToken = GetBFRAuthToken();
                        if (!Utilities.isBlank(authToken)) {
                            Map<String, String> httpHeader = new HashMap<>();
                            httpHeader.put("User-Agent", basics.UA);
                            httpHeader.put("X-Organization-Id", "Mossbets/v1");
                            httpHeader.put("Authorization", "Bearer " + authToken);
                            httpHeader.put("x-api-key", props.geniusXApiKey());

                            JSONArray legs = new JSONArray();
                            try (final java.sql.Connection betDb
                                    = db.ReadDataSource("bets").getConnection();
                                    PreparedStatement ps = betDb.prepareStatement(
                                            "SELECT IF(live_bet=0,'PreMatch','InPlay') AS game_state"
                                            + ",odd_value as odds,selection_id,status "
                                            + "FROM sports_bet_slip WHERE bet_id=?");) {
                                ps.setLong(1, bet.getBetId());
                                try (ResultSet rs = ps.executeQuery()) {
                                    while (rs.next()) {
                                        legs.put(new JSONObject()
                                                .put("price", Utilities
                                                        .Round2Decimal(rs.getDouble("odds")))
                                                .put("payoutPrice", Utilities
                                                        .Round2Decimal(rs.getDouble("odds")))
                                                .put("gameState", rs.getString("game_state"))
                                                .put("status", (rs.getInt("status") == 7)
                                                        ? "Cancelled" : "Settled")
                                                .put("betgeniusContent", new JSONObject()
                                                        .put("selectionId",
                                                                rs.getString("selection_id"))));
                                    }
                                } catch (SQLException s) {
                                }
                            } catch (SQLException s) {
                            }

                            if (legs.length() > 0) {
                                Instant httpTime = Instant.now();
                                Map<String, Object> httpResponse
                                        = ApiCalls.sendHttpJsonPostData(props.geniusRiskBfrUrl(),
                                                new JSONObject()
                                                        .put("id", String.valueOf(bet.getBetId()))
                                                        .put("betPlacedTimestampUTC", httpTime)
                                                        .put("betUpdatedTimestampUTC", httpTime)
                                                        .put("bookmakerName", "mossbets")
                                                        .put("payout", payout)
                                                        .put("playerId", betInfo.get("profileId").toString())
                                                        .put("totalStake", ((double) betInfo.get("stake")
                                                                - (double) betInfo.get("exciseTax")))
                                                        .put("currencyCode", betInfo.get("currency"))
                                                        .put("priority", 1)
                                                        .put("status", ((bet.getStatus() == 7)
                                                                ? "Cancelled" : "Settled"))
                                                        .put("legs", legs).toString(),
                                                httpHeader,
                                                basics,
                                                logger, false);
                                logger.info(Utilities.getLogPreString("BetSettlement")
                                        + "handleBetSettlement()"
                                        + "|BetId:" + bet.getBetId()
                                        + "|BetReference:" + betInfo.get("betReference")
                                        + "|BetStatus: " + bet.getStatus()
                                        + "|pId: " + betInfo.get("profileId") + "-" + eData.getString("msisdn")
                                        + "|BFR Settlement Results =>" + httpResponse.toString());

                                geniusBFR.put("resulted", false);
                                if ((int) httpResponse.get("statusCode") == 200) {
                                    geniusBFR.put("resulted", true);
                                }

                                eData.put("genius_bfr", geniusBFR);
                                RabbitUtils.updateStatements(db.WriteDataSource("bets"),
                                        "UPDATE sports_bet SET extra_data=? WHERE bet_id=? LIMIT 1",
                                        new Object[]{eData.toString(), bet.getBetId()});
                            }
                        }
                    }
                }
            } catch (Exception e) {
            }
        } catch (Exception e) {
            logger.error(Utilities.getLogPreString("BetSettlement")
                    + "handleBetSettlement()"
                    + "-" + queueName
                    + "|Took " + Utilities.CalculateTAT(tat) + " mSec(s)"
                    + "|Exception:", e);
            processed = false;
        }

        return processed;
    }

    /**
     * cleanup
     */
    @PreDestroy
    @SuppressWarnings("UseSpecificCatch")
    void cleanup() {
        logger.info(Utilities.getLogPreString("BetSettlement")
                + "cleanup()"
                + "|Shutting DOWN RabbitMQ Consumer gracefully...");
        running = false;
        try {
            if (connection != null && connection.isOpen()) {
                connection.close();
            }

            logger.info(Utilities.getLogPreString("BetSettlement")
                    + "cleanup()"
                    + "|RabbitMQ Connection closed");
        } catch (Exception e) {
            logger.fatal(Utilities.getLogPreString("BetSettlement")
                    + "cleanup()"
                    + "|Exception", e);
        }
    }

    /**
     * isHealthy
     *
     * @return
     */
    public boolean isHealthy() {
        return connection != null && connection.isOpen();
    }

    /**
     * reconnectWithDelay
     */
    private void reconnectWithDelay() {
        reconnectAttempt++;
        int delay = calculateBackoff(reconnectAttempt);

        logger.warnf(Utilities.getLogPreString("BetSettlement")
                + "reconnectWithDelay()"
                + "|Scheduling Reconnection in %d seconds (attempt #%d)...",
                delay, reconnectAttempt);

        scheduler.schedule(this::connectWithBackoff, delay, TimeUnit.SECONDS);
    }

    @Scheduled(every = "10m", concurrentExecution = Scheduled.ConcurrentExecution.SKIP)
    public void checkIfAppIsRunning() {
        if (isHealthy()) {
            logger.info(Utilities.getLogPreString("BetSettlement")
                    + "AppChecks() => ALIVE");
        } else if (running) {
            logger.info(Utilities.getLogPreString("BetSettlement")
                    + "AppChecks()"
                    + "|RabbitMQ Connection is DOWN. Attempting to reconnect...");
            reconnectWithDelay();
        }
    }

    /**
     * acknowledge
     *
     * @param channel
     * @param deliveryTag
     * @throws IOException
     */
    public void successAck(Channel channel, long deliveryTag) throws IOException {
        try {
            if (channel != null && channel.isOpen()) {
                channel.basicAck(deliveryTag, false);
                logger.infof(Utilities.getLogPreString("BetSettlement")
                        + "successAck()"
                        + "|ACK [%s] tag:%d", "Success", deliveryTag);
            }
        } catch (IOException e) {
            throw e;
        }
    }

    /**
     *
     * @param channel
     * @param deliveryTag
     * @param requeue
     */
    public void rejectAck(Channel channel, long deliveryTag, boolean requeue) {
        try {
            if (channel != null && channel.isOpen()) {
                channel.basicReject(deliveryTag, requeue);
                logger.infof(Utilities.getLogPreString("BetSettlement")
                        + "rejectAck()"
                        + "|ACK [%s] tag:%d", "Reject", deliveryTag);
            }
        } catch (IOException e) {
            logger.error(Utilities.getLogPreString("BetSettlement")
                    + "rejectAck()"
                    + "IOException:" + e.getMessage()
                    + "|ACK Failed tag:" + deliveryTag);
        }
    }

    /**
     * generateSignature
     *
     * @param ipAddress
     * @return
     */
    private String generateSignature(String ipAddress) {
        return "signature";
    }
}

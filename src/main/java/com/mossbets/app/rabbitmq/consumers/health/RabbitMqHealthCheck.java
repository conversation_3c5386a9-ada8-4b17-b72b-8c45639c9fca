package com.mossbets.app.rabbitmq.consumers.health;

import com.mossbets.app.rabbitmq.consumers.LiveBetting;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.eclipse.microprofile.health.HealthCheckResponse;
import org.eclipse.microprofile.health.Readiness;

@Readiness
@ApplicationScoped
public class RabbitMqHealthCheck {

    @Inject
    LiveBetting consumer;

    public HealthCheckResponse call() {
        return consumer.isHealthy()
                ? HealthCheckResponse.up("RabbitMQ")
                : HealthCheckResponse.down("RabbitMQ");
    }
}

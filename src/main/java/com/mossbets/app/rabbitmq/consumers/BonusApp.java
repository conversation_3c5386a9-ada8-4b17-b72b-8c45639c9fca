package com.mossbets.app.rabbitmq.consumers;

import com.mossbets.app.rabbitmq.models.Bonus;
import com.mossbets.app.rabbitmq.utils.RabbitUtils;
import com.mossbets.app.resources.DB;
import com.mossbets.app.utils.MailUtils;
import com.mossbets.app.utils.Queue;
import com.mossbets.app.utils.Utilities;
import com.mossbets.app.utils.props.Basics;
import com.mossbets.app.utils.props.Props;
import com.rabbitmq.client.AMQP;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;
import com.rabbitmq.client.DefaultConsumer;
import com.rabbitmq.client.Envelope;
import io.quarkus.runtime.ShutdownEvent;
import io.quarkus.runtime.StartupEvent;
import io.quarkus.scheduler.Scheduled;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.event.Observes;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.SQLIntegrityConstraintViolationException;
import java.time.Instant;
import java.util.LinkedHashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import org.codehaus.jettison.json.JSONObject;
import org.jboss.logging.Logger;

@ApplicationScoped
public class BonusApp {

    @Inject
    Props props;

    @Inject
    Basics basics = new Basics();

    @Inject
    DB db;

    @Inject
    private MailUtils MailUtils;

    @Inject
    private Queue Queue;

    @Inject
    Logger logger;

    @Inject
    @Named("walletExecutor")
    ExecutorService es;

    @Inject
    ScheduledExecutorService scheduler;

    private Connection connection;
    private int reconnectAttempt = 0;
    private final int maxBackoffSeconds = 60;
    private volatile boolean running = true;

    void onStart(@Observes StartupEvent ev) {
        logger.info(Utilities.getLogPreString("BonusApp")
                + "onStart()"
                + "|Starting RabbitMQ Consumer...");
        scheduleReconnect(0);
    }

    void onStop(@Observes ShutdownEvent event) {
        logger.info(Utilities.getLogPreString("BonusApp")
                + "onStop()"
                + "|Application shutting down...");
        cleanup();
    }

    @PostConstruct
    void startOnAppBoot() {
        scheduleReconnect(0); // ✅ triggers startup
    }

    /**
     * scheduleReconnect
     *
     * @param delaySeconds
     */
    private void scheduleReconnect(int delaySeconds) {
        scheduler.schedule(this::connectWithBackoff, delaySeconds, TimeUnit.SECONDS);
    }

    /**
     * connectWithBackoff
     */
    private void connectWithBackoff() {
        if (!running) {
            return;
        }

        try {
            connectAndStartConsuming();
            logger.info(Utilities.getLogPreString("BonusApp")
                    + "connectWithBackoff()"
                    + "|RabbitMQ Consumer connected and consuming....");
            reconnectAttempt = 0; // reset on success
        } catch (Exception e) {
            reconnectAttempt++;
            int backoff = calculateBackoff(reconnectAttempt);
            logger.errorf(Utilities.getLogPreString("BonusApp")
                    + "connectWithBackoff()"
                    + "|Reconnect failed (attempt #%d). Retrying in %ds...",
                    reconnectAttempt, backoff);
            scheduleReconnect(backoff);
        }
    }

    /**
     * calculateBackoff
     *
     * @param attempt
     * @return
     */
    private int calculateBackoff(int attempt) {
        int base = (int) Math.min(Math.pow(2, attempt), maxBackoffSeconds);
        int jitter = ThreadLocalRandom.current().nextInt(0, 5);
        return base + jitter; // Exponential + jitter
    }

    /**
     * createFactory
     *
     * @return
     */
    private ConnectionFactory createFactory() {
        ConnectionFactory factory = new ConnectionFactory();
        factory.setHost(props.rabbitMqHost());
        factory.setPort(props.rabbitMqPort());
        factory.setVirtualHost(props.rabbitMqVhost());
        factory.setUsername(props.rabbitMqUsername());
        factory.setPassword(props.rabbitMqPassword());
        factory.setTopologyRecoveryEnabled(props.rabbitMqConRecovery());
        factory.setAutomaticRecoveryEnabled(props.rabbitMqTopologyRecovery());
        factory.setNetworkRecoveryInterval(props.rabbitMqNetworkRecovery());
        return factory;
    }

    /**
     * connectAndStartConsuming
     *
     * @throws Exception
     */
    private void connectAndStartConsuming() throws Exception {
        connection = createFactory().newConnection();
        connection.addShutdownListener(cause -> {
            logger.info(Utilities.getLogPreString("BonusApp")
                    + "connectAndStartConsuming()"
                    + "|Connection shutdown: " + cause.getMessage());
            if (running) {
                scheduleReconnect(0); // retry again
            }
        });

        for (String queue : props.bonusQueues()) {
            String queueName = props.rabbitMqPrefix().concat("_" + queue + "_QUEUE");
            String exchange = props.rabbitMqPrefix().concat("_" + queue + "_EXCHANGE");
            String route = props.rabbitMqPrefix().concat("_" + queue + "_ROUTE");

            Channel channel = connection.createChannel();
            channel.queueDeclare(queueName, true, false, false, null);
            channel.exchangeDeclare(exchange, "direct", true, false, null);
            channel.queueBind(queueName, exchange, route);
            channel.basicQos(props.rabbitMqPrefetchCount());
            channel.basicConsume(queueName, false, new DefaultConsumer(channel) {
                @Override
                @SuppressWarnings("UseSpecificCatch")
                public void handleDelivery(String consumerTag, Envelope envelope,
                        AMQP.BasicProperties properties, byte[] body) {
                    long deliveryTag = envelope.getDeliveryTag();
                    try {
                        es.submit(() -> handleMessage(body, queueName, channel, deliveryTag));
                    } catch (Exception ex) {
                        logger.error(Utilities.getLogPreString("BonusApp")
                                + "connectAndStartConsuming()"
                                + "|Queue " + queueName
                                + "|ConsumerTag:" + consumerTag
                                + "|Exception: " + ex.getMessage());
                        rejectAck(channel, deliveryTag, true);
                    }
                }
            });

            logger.infof(Utilities.getLogPreString("BonusApp")
                    + "connectAndStartConsuming()"
                    + "|Queue [%s]"
                    + "|Exchange [%s]"
                    + "|Route [%s]", queueName, exchange, route);
        }
    }

    /**
     * cleanup
     */
    @PreDestroy
    @SuppressWarnings("UseSpecificCatch")
    void cleanup() {
        logger.info(Utilities.getLogPreString("BonusApp")
                + "cleanup()"
                + "|Shutting DOWN RabbitMQ Consumer gracefully...");
        running = false;
        try {
            if (connection != null && connection.isOpen()) {
                connection.close();
            }

            logger.info(Utilities.getLogPreString("BonusApp")
                    + "cleanup()"
                    + "|RabbitMQ Connection closed");
        } catch (Exception e) {
            logger.fatal(Utilities.getLogPreString("BonusApp")
                    + "cleanup()"
                    + "|Exception", e);
        }
    }

    /**
     * isHealthy
     *
     * @return
     */
    public boolean isHealthy() {
        return connection != null && connection.isOpen();
    }

    /**
     * reconnectWithDelay
     */
    private void reconnectWithDelay() {
        reconnectAttempt++;
        int delay = calculateBackoff(reconnectAttempt);

        logger.warnf(Utilities.getLogPreString("BonusApp")
                + "reconnectWithDelay()"
                + "|Scheduling Reconnection in %d seconds (attempt #%d)...",
                delay, reconnectAttempt);

        scheduler.schedule(this::connectWithBackoff, delay, TimeUnit.SECONDS);
    }

    @Scheduled(every = "10m", concurrentExecution = Scheduled.ConcurrentExecution.SKIP)
    public void checkIfAppIsRunning() {
        if (isHealthy()) {
            logger.info(Utilities.getLogPreString("BonusApp")
                    + "AppChecks() => ALIVE");
        } else if (running) {
            logger.info(Utilities.getLogPreString("BonusApp")
                    + "AppChecks()"
                    + "|RabbitMQ Connection is DOWN. Attempting to reconnect...");
            reconnectWithDelay();
        }
    }

    /**
     * acknowledge
     *
     * @param channel
     * @param deliveryTag
     * @throws IOException
     */
    public void successAck(Channel channel, long deliveryTag) throws IOException {
        try {
            if (channel != null && channel.isOpen()) {
                channel.basicAck(deliveryTag, false);
                logger.infof(Utilities.getLogPreString("BonusApp")
                        + "successAck()"
                        + "|ACK [%s] tag:%d", "Success", deliveryTag);
            }
        } catch (IOException e) {
            throw e;
        }
    }

    /**
     *
     * @param channel
     * @param deliveryTag
     * @param requeue
     */
    public void rejectAck(Channel channel, long deliveryTag, boolean requeue) {
        try {
            if (channel != null && channel.isOpen()) {
                channel.basicReject(deliveryTag, requeue);
                logger.infof(Utilities.getLogPreString("BonusApp")
                        + "rejectAck()"
                        + "|ACK [%s] tag:%d", "Reject", deliveryTag);
            }
        } catch (IOException e) {
            logger.error(Utilities.getLogPreString("BonusApp")
                    + "rejectAck()"
                    + "IOException:" + e.getMessage()
                    + "|ACK Failed tag:" + deliveryTag);
        }
    }

    /**
     * handleMessage
     *
     * @param body
     * @param queueName
     * @param channel
     * @param deliveryTag
     */
    @SuppressWarnings("UseSpecificCatch")
    private void handleMessage(byte[] body, String queueName, Channel channel, long deliveryTag) {
        Instant tat = Instant.now();
        try {
            if (handleFreebets(body, queueName)) {
                successAck(channel, deliveryTag);
            } else {
                rejectAck(channel, deliveryTag, true);
            }
        } catch (Exception e) {
            rejectAck(channel, deliveryTag, true);

            logger.error(Utilities.getLogPreString("BonusApp")
                    + "handleMessage()"
                    + "-" + queueName
                    + "|Took " + Utilities.CalculateTAT(tat) + " mSec(s)"
                    + "|Exception:" + e.getMessage());
        }
    }

    /**
     * handleFreebets
     *
     * @param body
     * @param queueName
     * @return
     */
    @SuppressWarnings("UseSpecificCatch")
    private boolean handleFreebets(byte[] body, String queueName) {
        boolean processed = false;
        String message = new String(body, StandardCharsets.UTF_8);
        try {
            logger.info(Utilities.getLogPreString("BonusApp")
                    + "handleFreebets()"
                    + "|Received"
                    + "-" + queueName
                    + "- Msg:" + message);

            Bonus bonus = new Bonus();
            try {
                JSONObject obj = new JSONObject(message);
                bonus.setProfileId(obj.getLong("profile_id"));
                bonus.setPromoId(obj.getLong("promo_id"));
                bonus.setAmount(obj.getDouble("amount"));
                bonus.setIpAddress(obj.getString("ip_address"));
                bonus.setType(obj.getString("type").toUpperCase());
                bonus.setSignature(obj.getString("signature"));
                bonus.setExpiryPeriod(obj.getInt("expiry_period"));
                bonus.setDate(obj.getString("date"));
                bonus.setUniqueId(obj.getString("unique_id"));

                bonus.setUserId(0);
                if (obj.has("user_id")) {
                    bonus.setUserId(obj.getLong("user_id"));
                }
            } catch (Exception js) {
                logger.error(Utilities.getLogPreString("BonusApp")
                        + "handleFreebets()"
                        + "-" + queueName
                        + "|Msg:" + message
                        + "|Decode Exception:" + js.getMessage(), js);
                return true;
            }

            if (!Utilities.FindInStringArray(new String[]{"FREEBET", "BONUS"},
                    bonus.getType().toUpperCase())) {
                logger.error(Utilities.getLogPreString("BonusApp")
                        + "handleFreebets()"
                        + "|ProfileId:" + bonus.getProfileId()
                        + "|PromoId:" + bonus.getPromoId()
                        + "|Invalid Type:" + bonus.getType());
                return true;
            }

            if (Utilities.isBlank(bonus.getUniqueId())) {
                logger.error(Utilities.getLogPreString("BonusApp")
                        + "handleFreebets()"
                        + "|ProfileId:" + bonus.getProfileId()
                        + "|PromoId:" + bonus.getPromoId()
                        + "|Type:" + bonus.getType()
                        + "|Invalid UniqueId:" + bonus.getUniqueId());
                return true;
            }

            Map<String, Object> profile = null;
            Map<String, Object> userMap = null;
            Map<String, Object> promoMap = null;
            try (final java.sql.Connection dbConn = db.ReadDataSource("profile").getConnection();) {
                profile = RabbitUtils.getCustomerDetails(bonus.getProfileId(), dbConn);
                if (bonus.getUserId() > 0) {
                    userMap = RabbitUtils.getUserDetails(bonus.getUserId(), dbConn);
                }
                promoMap = RabbitUtils.getBonusDetails(bonus.getPromoId(), dbConn);
            } catch (Exception e) {
            }

            if (null == profile) {
                logger.error(Utilities.getLogPreString("BonusApp")
                        + "handleFreebets()"
                        + "|ProfileId:" + bonus.getProfileId()
                        + "|PromoId:" + bonus.getPromoId()
                        + "|Invalid Profile");
                return true;
            }

            if (null == userMap && bonus.getUserId() > 0) {
                logger.error(Utilities.getLogPreString("BonusApp")
                        + "handleFreebets()"
                        + "|ProfileId:" + bonus.getProfileId()
                        + "|PromoId:" + bonus.getPromoId()
                        + "|Invalid UserId");
                return true;
            }

            if (null == promoMap) {
                logger.error(Utilities.getLogPreString("BonusApp")
                        + "handleFreebets()"
                        + "|ProfileId:" + bonus.getProfileId()
                        + "|PromoId:" + bonus.getPromoId()
                        + "|Invalid Promotions Data");
                return true;
            }

            String currency = "KES";
            if (bonus.getAmount() <= 0) {
                bonus.setAmount((double) promoMap.get("bonus"));
            }

            if (bonus.getExpiryPeriod() < 1) {
                bonus.setExpiryPeriod((int) promoMap.get("expiryPeriod"));
            }

            try (java.sql.Connection dbP = db.WriteDataSource("profile").getConnection();) {
                dbP.setAutoCommit(false);
                if (bonus.getType().equalsIgnoreCase("FREEBET")) {
                    try (final PreparedStatement createFb
                            = dbP.prepareStatement("INSERT INTO profile_bonus"
                                    + "(profile_id,bonus_id,bonus_amount,max_win"
                                    + ",status,extra_data,expiry_date,created_at) "
                                    + "VALUES(?,?,?,?,?,?,DATE_ADD(NOW(), INTERVAL ? HOUR),NOW())",
                                    PreparedStatement.RETURN_GENERATED_KEYS);) {
                        createFb.setLong(1, bonus.getProfileId());
                        createFb.setLong(2, bonus.getPromoId());
                        createFb.setDouble(3, bonus.getAmount());
                        createFb.setDouble(4, (double) promoMap.get("maxWin"));
                        createFb.setInt(5, 1);
                        createFb.setString(6, new JSONObject()
                                .put("name", promoMap.get("promoName"))
                                .put("type", promoMap.get("component"))
                                .put("ip", bonus.getProfileId())
                                .put("bonus_percentage", 1.0)
                                .put("expiry_period_in_hours", bonus.getExpiryPeriod())
                                .toString());
                        createFb.setInt(7, bonus.getExpiryPeriod());
                        long bonusId = createFb.executeUpdate();
                        try (ResultSet rs = createFb.getGeneratedKeys()) {
                            if (rs.next()) {
                                bonusId = rs.getLong(1);
                            }
                        }
                        dbP.commit();
                        processed = true;
                    } catch (Exception e) {
                        throw e;
                    }
                }

                if (bonus.getType().toUpperCase().equalsIgnoreCase("BONUS")) {
                    if ((int) profile.get("status") == 5) {
                        dbP.rollback();

                        Set<String> emailList = new LinkedHashSet<>();
                        emailList.add("<EMAIL>");
                        emailList.add("<EMAIL>");
                        emailList.add("<EMAIL>");
                        emailList.add(userMap.get("email").toString());

                        MailUtils.SendMail(
                                String.join(",", emailList),
                                userMap.get("name").toString(),
                                new StringBuilder()
                                        .append("<h4>Failed Trivia Bonus</h4>")
                                        .append("<span>ID:</span>")
                                        .append(bonus.getPromoId())
                                        .append("<span>BonusName:</span>")
                                        .append(promoMap.get("promoName"))
                                        .append("<span>MSISDN:</span>")
                                        .append(profile.get("msisdn"))
                                        .append("<br/><span>Reason:</span>")
                                        .append("User Does not have an Active Wallet")
                                        .append("<br/><span>Amount:</span>")
                                        .append("KES")
                                        .append(".")
                                        .append(bonus.getAmount())
                                        .append("<br/><span>Date:</span>")
                                        .append(bonus.getDate()).toString(),
                                "Trivia " + bonus.getType(), null);
                        return true;
                    }

                    String updateBalanceSql = "UPDATE profile_balance "
                            + "SET balance=balance+?,bonus=bonus+? WHERE profile_id=? "
                            + "LIMIT 1";
                    try (final PreparedStatement psBalance
                            = dbP.prepareStatement(updateBalanceSql);) {
                        psBalance.setDouble(1, 0);
                        psBalance.setDouble(2, bonus.getAmount());
                        psBalance.setLong(3, bonus.getProfileId());
                        int updateState = psBalance.executeUpdate();
                        if (updateState < 1) {
                            throw new SQLException("Profile Wallet Update failed "
                                    + "|" + bonus.getPromoId()
                                    + "|" + profile.get("msisdn")
                                    + "|" + promoMap.get("promoName")
                                    + "|" + bonus.getAmount());
                        }
                    } catch (SQLException e) {
                        throw e;
                    }

                    String insertTrxnSql = "INSERT INTO transaction(profile_id"
                            + ",reference_type_id,transaction_type_id,reference_id"
                            + ",amount,currency,source,description,extra_data,created_at) "
                            + "VALUES(?,?,?,?,?,?,?,?,?,NOW())";
                    try (final java.sql.Connection dbW
                            = db.WriteDataSource("trxn").getConnection();
                            final PreparedStatement ps = dbW.prepareStatement(
                                    insertTrxnSql, PreparedStatement.RETURN_GENERATED_KEYS);) {
                        dbW.setAutoCommit(false);
                        ps.setLong(1, bonus.getProfileId());
                        ps.setInt(2, 36);//Trivia Bonus
                        ps.setInt(3, basics.TRANSACTION_TYPE_BONUS_BOOST);
                        ps.setString(4, bonus.getUniqueId());
                        ps.setDouble(5, bonus.getAmount());
                        ps.setString(6, currency);
                        ps.setString(7, "TRIVIA_BONUS_AWARDS");
                        ps.setString(8, promoMap.get("component").toString()
                                + " Bonus Awards:"
                                + "" + promoMap.get("promoName"));
                        ps.setString(9, new JSONObject()
                                .put("receiptNo", bonus.getPromoId())
                                .put("msisdn", profile.get("msisdn"))
                                .put("accNo", profile.get("accNo"))
                                .put("bonusIssued", bonus.getAmount())
                                .toString());
                        long trxnId = ps.executeUpdate();
                        try (ResultSet rs = ps.getGeneratedKeys()) {
                            if (rs.next()) {
                                trxnId = rs.getLong(1);
                            }
                        }

                        if (trxnId < 1) {
                            throw new SQLException("System TrxnId creation failed "
                                    + "|" + bonus.getPromoId()
                                    + "|" + profile.get("msisdn")
                                    + "|" + promoMap.get("promoName")
                                    + "|" + bonus.getAmount());
                        }

                        dbP.commit();
                        dbW.commit();

                        processed = true;
                    } catch (Exception e) {
                        dbP.rollback();
                        throw e;
                    }
                }
            } catch (SQLIntegrityConstraintViolationException e) {
                // This is thrown for duplicate‑entry / unique constraint violations
                // For MySQL, vendor code 1062 indicates duplicate entry
                if (e.getErrorCode() == 1062 || e.getSQLState().startsWith("23")) {
                    logger.error(Utilities.getLogPreString("BonusApp")
                            + "handleFreebets()"
                            + "|ProfileId:" + bonus.getProfileId()
                            + "|PromoId:" + bonus.getPromoId()
                            + "|" + bonus.getPromoId()
                            + "|" + profile.get("msisdn")
                            + "|" + promoMap.get("promoName")
                            + "|" + bonus.getAmount()
                            + "|Duplicate‑entry / unique constraint violations"
                            + "|Exception:" + e.getMessage());
                    return true;
                }
                throw e;
            } catch (Exception e) {
                throw e;
            }

            if (processed) {
                try {
                    RabbitUtils.LogUserActions(db.WriteDataSource("profile"),
                            bonus.getUserId(), "Trivia " + bonus.getType(),
                            "Created a " + bonus.getType()
                            + " of " + currency + "." + bonus.getAmount()
                            + ". " + promoMap.get("promoName")
                            + ". Expiry " + bonus.getExpiryPeriod()
                            + ". UniqueId:" + bonus.getUniqueId()
                            + ". MSISDN " + profile.get("msisdn"));

                    String messageOut = "Congrats! You've been awarded a "
                            + bonus.getType() + ". {value}"
                            + "\nLogin NOW and Place your bet {site}"
                            + "\n{expiry}"
                            + "\nT&Cs Apply"
                            + "\nHelpline: " + basics.HelpLine;
                    messageOut = messageOut
                            .replace("{value}", currency + "." + bonus.getAmount())
                            .replace("{site}", (bonus.getType()
                                    .equalsIgnoreCase("FREEBET"))
                                    ? "https://mossbets.com/freebet" : "https://mossbets.com")
                            .replace("{expiry}", (bonus.getType()
                                    .equalsIgnoreCase("FREEBET"))
                                    ? "Expiry " + bonus.getExpiryPeriod() + " Hours" : "");

                    long outboxId = RabbitUtils.createSMSOutBox(bonus.getProfileId(),
                            basics.SenderId, messageOut, "DEPOSIT",
                            db.WriteDataSource("profile"));

                    String smsQ = Utilities
                            .getRandomValue(props.SmsOutboxQueue());
                    Queue.publishMessage(
                            new JSONObject()
                                    .put("campaign_id", outboxId)
                                    .put("message_pages", 1)
                                    .put("message", messageOut)
                                    .put("sms-id", "QUICKSENDVERIFICATION")
                                    .put("network_regex", 1)
                                    .put("network", profile.get("network").toString())
                                    .put("alert_type", "TRANSACTIONAL")
                                    .put("recipients", profile.get("msisdn"))
                                    .put("outbox_id", outboxId)
                                    .put("short_code", basics.SenderId)
                                    .put("gateway", 1)
                                    .put("dlr_url", "")
                                    .put("auth_token", "auth_token_api")
                                    .put("date_created",
                                            Utilities.now("yyyy-MM-dd HH:mm:ss"))
                                    .toString(),
                            smsQ,
                            smsQ,
                            smsQ,
                            null,
                            null);
                } catch (Exception e) {
                    logger.error(Utilities.getLogPreString("BonusApp")
                            + "handleFreebets()"
                            + "|Send Outbox"
                            + "|" + profile.get("msisdn")
                            + "|Exception:" + e.getMessage());
                }
            }
        } catch (Exception e) {
            logger.error(Utilities.getLogPreString("BonusApp")
                    + "handleFreebets()"
                    + "-" + queueName
                    + "|Last Exception:", e);
            processed = false;
        }

        return processed;

    }
}

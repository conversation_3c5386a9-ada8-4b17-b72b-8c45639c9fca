package com.mossbets.app.rabbitmq.consumers;

import com.mossbets.app.rabbitmq.models.KraStake;
import com.mossbets.app.rabbitmq.utils.RabbitUtils;
import com.mossbets.app.resources.DB;
import com.mossbets.app.utils.ApiCalls;
import com.mossbets.app.utils.JedisUtils;
import com.mossbets.app.utils.Queue;
import com.mossbets.app.utils.Utilities;
import com.mossbets.app.utils.props.Basics;
import com.mossbets.app.utils.props.Props;
import com.rabbitmq.client.*;
import io.quarkus.runtime.ShutdownEvent;
import io.quarkus.runtime.StartupEvent;
import io.quarkus.scheduler.Scheduled;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.event.Observes;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.SQLIntegrityConstraintViolationException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import org.codehaus.jettison.json.JSONArray;
import org.codehaus.jettison.json.JSONException;
import org.codehaus.jettison.json.JSONObject;
import org.jboss.logging.Logger;

@ApplicationScoped
public class KRAIntegrations {

    @Inject
    Props props;

    @Inject
    Basics basics = new Basics();

    @Inject
    DB db;

    @Inject
    Logger logger;

    @Inject
    private Queue Queue;

    @Inject
    @Named("kraExecutor")
    ExecutorService es;

    @Inject
    ScheduledExecutorService scheduler;

    private Connection connection;
    private int reconnectAttempt = 0;
    private final int maxBackoffSeconds = 60;
    private volatile boolean running = true;

    void onStart(@Observes StartupEvent ev) {
        logger.info(Utilities.getLogPreString("KRAIntegrations")
                + "onStart()"
                + "|Starting RabbitMQ Consumer...");
        scheduleReconnect(0);
    }

    void onStop(@Observes ShutdownEvent event) {
        logger.info(Utilities.getLogPreString("KRAIntegrations")
                + "onStop()"
                + "|Application shutting down...");
        cleanup();
    }

    @PostConstruct
    void startOnAppBoot() {
        scheduleReconnect(0); // ✅ triggers startup
    }

    /**
     * scheduleReconnect
     *
     * @param delaySeconds
     */
    private void scheduleReconnect(int delaySeconds) {
        scheduler.schedule(this::connectWithBackoff, delaySeconds, TimeUnit.SECONDS);
    }

    /**
     * connectWithBackoff
     */
    private void connectWithBackoff() {
        if (!running) {
            return;
        }

        try {
            connectAndStartConsuming();
            logger.info(Utilities.getLogPreString("KRAIntegrations")
                    + "connectWithBackoff()"
                    + "|RabbitMQ Consumer connected and consuming....");
            reconnectAttempt = 0; // reset on success
        } catch (Exception e) {
            reconnectAttempt++;
            int backoff = calculateBackoff(reconnectAttempt);
            logger.errorf(Utilities.getLogPreString("KRAIntegrations")
                    + "connectWithBackoff()"
                    + "|Reconnect failed (attempt #%d). Retrying in %ds...",
                    reconnectAttempt, backoff);
            scheduleReconnect(backoff);
        }
    }

    /**
     * calculateBackoff
     *
     * @param attempt
     * @return
     */
    private int calculateBackoff(int attempt) {
        int base = (int) Math.min(Math.pow(2, attempt), maxBackoffSeconds);
        int jitter = ThreadLocalRandom.current().nextInt(0, 5);
        return base + jitter; // Exponential + jitter
    }

    /**
     * createFactory
     *
     * @return
     */
    private ConnectionFactory createFactory() {
        ConnectionFactory factory = new ConnectionFactory();
        factory.setHost(props.rabbitMqHost());
        factory.setPort(props.rabbitMqPort());
        factory.setVirtualHost(props.rabbitMqVhost());
        factory.setUsername(props.rabbitMqUsername());
        factory.setPassword(props.rabbitMqPassword());
        factory.setTopologyRecoveryEnabled(props.rabbitMqConRecovery());
        factory.setAutomaticRecoveryEnabled(props.rabbitMqTopologyRecovery());
        factory.setNetworkRecoveryInterval(props.rabbitMqNetworkRecovery());
        return factory;
    }

    /**
     * cleanup
     */
    @PreDestroy
    @SuppressWarnings("UseSpecificCatch")
    void cleanup() {
        logger.info(Utilities.getLogPreString("KRAIntegrations")
                + "cleanup()"
                + "|Shutting DOWN RabbitMQ Consumer gracefully...");
        running = false;
        try {
            if (connection != null && connection.isOpen()) {
                connection.close();
            }

            logger.info(Utilities.getLogPreString("KRAIntegrations")
                    + "cleanup()"
                    + "|RabbitMQ Connection closed");
        } catch (Exception e) {
            logger.fatal(Utilities.getLogPreString("KRAIntegrations")
                    + "cleanup()"
                    + "|Exception", e);
        }
    }

    /**
     * isHealthy
     *
     * @return
     */
    public boolean isHealthy() {
        return connection != null && connection.isOpen();
    }

    /**
     * reconnectWithDelay
     */
    private void reconnectWithDelay() {
        reconnectAttempt++;
        int delay = calculateBackoff(reconnectAttempt);

        logger.warnf(Utilities.getLogPreString("KRAIntegrations")
                + "reconnectWithDelay()"
                + "|Scheduling Reconnection in %d seconds (attempt #%d)...",
                delay, reconnectAttempt);

        scheduler.schedule(this::connectWithBackoff, delay, TimeUnit.SECONDS);
    }

    @Scheduled(every = "10m", concurrentExecution = Scheduled.ConcurrentExecution.SKIP)
    public void checkIfAppIsRunning() {
        if (isHealthy()) {
            logger.info(Utilities.getLogPreString("KRAIntegrations")
                    + "AppChecks() => ALIVE");
        } else if (running) {
            logger.info(Utilities.getLogPreString("KRAIntegrations")
                    + "AppChecks()"
                    + "|RabbitMQ Connection is DOWN. Attempting to reconnect...");
            reconnectWithDelay();
        }
    }

    /**
     * acknowledge
     *
     * @param channel
     * @param deliveryTag
     * @throws IOException
     */
    public void successAck(Channel channel, long deliveryTag) throws IOException {
        try {
            if (channel != null && channel.isOpen()) {
                channel.basicAck(deliveryTag, false);
                logger.infof(Utilities.getLogPreString("KRAIntegrations")
                        + "successAck()"
                        + "|ACK [%s] tag:%d", "Success", deliveryTag);
            }
        } catch (IOException e) {
            throw e;
        }
    }

    /**
     *
     * @param channel
     * @param deliveryTag
     * @param requeue
     */
    public void rejectAck(Channel channel, long deliveryTag, boolean requeue) {
        try {
            if (channel != null && channel.isOpen()) {
                channel.basicReject(deliveryTag, requeue);
                logger.infof(Utilities.getLogPreString("KRAIntegrations")
                        + "rejectAck()"
                        + "|ACK [%s] tag:%d", "Reject", deliveryTag);
            }
        } catch (IOException e) {
            logger.error(Utilities.getLogPreString("KRAIntegrations")
                    + "rejectAck()"
                    + "IOException:" + e.getMessage()
                    + "|ACK Failed tag:" + deliveryTag);
        }
    }

    /**
     * connectAndStartConsuming
     *
     * @throws Exception
     */
    private void connectAndStartConsuming() throws Exception {
        connection = createFactory().newConnection();
        connection.addShutdownListener(cause -> {
            logger.info(Utilities.getLogPreString("KRAIntegrations")
                    + "connectAndStartConsuming()"
                    + "|Connection shutdown: " + cause.getMessage());
            if (running) {
                scheduleReconnect(0); // retry again
            }
        });

        for (String queue : props.KRAQueues()) {
            String queueName = props.rabbitMqPrefix().concat("_" + queue + "_QUEUE");
            String exchange = props.rabbitMqPrefix().concat("_" + queue + "_EXCHANGE");
            String route = props.rabbitMqPrefix().concat("_" + queue + "_ROUTE");

            Channel channel = connection.createChannel();
            channel.queueDeclare(queueName, true, false, false, null);
            channel.exchangeDeclare(exchange, "direct", true, false, null);
            channel.queueBind(queueName, exchange, route);
            channel.basicQos(props.rabbitMqPrefetchCount());
            channel.basicConsume(queueName, false, new DefaultConsumer(channel) {
                @Override
                @SuppressWarnings("UseSpecificCatch")
                public void handleDelivery(String consumerTag, Envelope envelope,
                        AMQP.BasicProperties properties, byte[] body) {
                    long deliveryTag = envelope.getDeliveryTag();
                    try {
                        es.submit(() -> handleMessage(body, queueName, channel, deliveryTag));
                    } catch (Exception ex) {
                        logger.error(Utilities.getLogPreString("KRAIntegrations")
                                + "connectAndStartConsuming()"
                                + "|Queue " + queueName
                                + "|ConsumerTag:" + consumerTag
                                + "|Exception: " + ex.getMessage());
                        rejectAck(channel, deliveryTag, true);
                    }
                }
            });

            logger.infof(Utilities.getLogPreString("KRAIntegrations")
                    + "connectAndStartConsuming()"
                    + "|Queue [%s]"
                    + "|Exchange [%s]"
                    + "|Route [%s]", queueName, exchange, route);
        }
    }

    /**
     * handleMessage
     *
     * @param body
     * @param queueName
     * @param channel
     * @param deliveryTag
     */
    @SuppressWarnings("UseSpecificCatch")
    private void handleMessage(byte[] body, String queueName, Channel channel, long deliveryTag) {
        try {
//            if (queueName.contains("KRA_RESULTS")) {
//                if (handleKRAResults(body, queueName)) {
//                    successAck(channel, deliveryTag);
//                } else {
//                    rejectAck(channel, deliveryTag, true);
//                }
//                return;
//            }
//
//            if (queueName.contains("KRA_STAKE")) {
//                if (handleKRAStake(body, queueName)) {
//                    successAck(channel, deliveryTag);
//                } else {
//                    rejectAck(channel, deliveryTag, true);
//                }
//                return;
//            }

            successAck(channel, deliveryTag);
        } catch (Exception e) {
            rejectAck(channel, deliveryTag, true);

            logger.error(Utilities.getLogPreString("KRAIntegrations")
                    + "handleMessage()"
                    + "-" + queueName
                    + "|Exception:" + e.getMessage());
        }
    }

    /**
     * handleKRAResults
     *
     * @param body
     * @param queueName
     * @return
     */
    @SuppressWarnings({"UseSpecificCatch", "null", "unused"})
    private boolean handleKRAResults(byte[] body, String queueName) {
        Instant tat = Instant.now();
        boolean processed = false;
        String message = new String(body, StandardCharsets.UTF_8);
        try {
            logger.info(Utilities.getLogPreString("KRAIntegrations")
                    + "handleKRAResults()"
                    + "|Received"
                    + "-" + queueName
                    + "- Msg:" + message);

            long betId;
            String betType;
            String betReference;
            try {
                JSONObject obj = new JSONObject(message);
                betId = obj.getLong("betId");
                betType = obj.getString("betType");
                betReference = obj.getString("betReference");
            } catch (Exception js) {
                logger.error(Utilities.getLogPreString("KRAIntegrations")
                        + "handleKRAResults()"
                        + "-" + queueName
                        + "|Took " + Utilities.CalculateTAT(tat) + " mSec(s)"
                        + "|Exception:" + js.getMessage());
                return true;
            }

            Map<String, Object> betInfo = null;
            Map<String, Object> custInfo = null;
            try (final java.sql.Connection dbConn = db.ReadDataSource("bets").getConnection();) {
                betInfo = RabbitUtils.getSportsBetDetails(betId, dbConn);
                if (betInfo != null) {
                    custInfo = RabbitUtils.getCustomerDetails((long) betInfo.get("profileId"), dbConn);
                }
            } catch (Exception e) {
            }

            if (null == betInfo || null == custInfo) {
                logger.info(Utilities.getLogPreString("KRAIntegrations")
                        + "handleKRAResults()"
                        + "|BetId:" + betId
                        + "|betReference:" + betReference
                        + "|Bet NOT FOUND!!!");
                return true;
            }

            int betStatus = (int) betInfo.get("status");
            if (betStatus == 0) {
                logger.info(Utilities.getLogPreString("KRAIntegrations")
                        + "handleKRAResults()"
                        + "|BetId:" + betId
                        + "|betReference:" + betReference
                        + "|Bet is NOT Resulted Fully!!!");
                return true;
            }

            if ((int) betInfo.get("kraReport") != 1) {
                logger.info(Utilities.getLogPreString("KRAIntegrations")
                        + "handleKRAResults()"
                        + "|BetId:" + betId
                        + "|betReference:" + betReference
                        + "|Bet is NOT To Be SENT to KRA!!!");
                return true;
            }

            JSONObject eData = new JSONObject(betInfo.get("eData").toString());

            Map<String, Object> check = checkBetTransmission(betReference, betType,
                    eData.getLong("msisdn"));
            if (null == check) {
                logger.info(Utilities.getLogPreString("KRAIntegrations")
                        + "handleKRAResults()"
                        + "|BetId:" + betId
                        + "|betReference:" + betReference
                        + "|Bet Had NOT been Transamitted"
                        + "|Bet is NOT To Be SENT to KRA!!!");
                return true;
            }

            if (Utilities.FindInIntegerArray(new int[]{2000}, (int) check.get("status"))
                    || !(check.get("postDate").equals("null"))) {
                logger.error(Utilities.getLogPreString("KRAIntegrations")
                        + "handleKRAResults()"
                        + "|BetId:" + betId
                        + "|betReference:" + betReference
                        + "|Results submitted!");
                return true;
            }

            Map<String, String> kraConfigs = null;
            String key = basics.KRASettingKey.replace("{clientId}",
                    betInfo.get("clientId").toString());
            String redisData = JedisUtils.selectOneData(key);
            if (!Utilities.isBlank(redisData)) {
                try {
                    @SuppressWarnings("unchecked")
                    Map<String, String> tempMap = new JSONObject(redisData).toMap();
                    kraConfigs = tempMap;
                } catch (JSONException e) {
                }
            }

            if (null == kraConfigs) {
                kraConfigs = getKraComplianceSettings((int) betInfo.get("clientId"));
                if (null == kraConfigs) {
                    logger.error(Utilities.getLogPreString("KRAIntegrations")
                            + "handleKRAResults()"
                            + "|BetId:" + betId
                            + "|betReference:" + betReference
                            + "|Invalid KRA Configs!");
                    return false;
                }

                JedisUtils.saveData(key, new JSONObject(kraConfigs).toString(), 60 * 50);
            }

            logger.info(Utilities.getLogPreString("KRAIntegrations")
                    + "handleKRAResults()"
                    + "|BetId:" + betId
                    + "|betReference:" + betReference
                    + "|PIN:" + kraConfigs.get("username"));

            key = basics.KRATokenKey.replace("{clientId}", betInfo.get("clientId").toString());
            String kraToken = JedisUtils.selectOneData(key);
            if (Utilities.isBlank(kraToken)) {
                kraToken = generateBearerToken(
                        kraConfigs.get("username").toLowerCase(),
                        kraConfigs.get("password"),
                        kraConfigs.get("domains"));
                if (null == kraToken) {
                    logger.error(Utilities.getLogPreString("KRAIntegrations")
                            + "handleKRAResults()"
                            + "|BetId:" + betId
                            + "|betReference:" + betReference
                            + "|PIN:" + kraConfigs.get("username")
                            + "|Failed to Generate Bearer Token!");
                    Thread.sleep(10000);
                    return false;
                }

                JedisUtils.saveData(key, kraToken, 60 * 50);
            }

            int noOfStakes = 1;

            double stake = (double) betInfo.get("stake");
            double payout = (double) betInfo.get("payout");
            double walletBalanceStake = ((double) custInfo.get("balance")
                    + (double) custInfo.get("bonus"));
            double winnings = (payout - stake);
            double withholdingTax = (double) betInfo.get("wTax");

            String outcome = "WIN";
            String postDesc = "OutcomeWon";
            if (betStatus != 1) {
                winnings = 0.0;
                withholdingTax = 0.0;

                outcome = "LOSE";
                postDesc = "OutcomeLost";
            }

            String transactionDate = DateFormatter(Utilities.now("yyyy-MM-dd HH:mm:ss"));
            String hash = kraConfigs.get("username")
                    + "" + transactionDate
                    + "" + noOfStakes;

            String expectedOutcomeTime = transactionDate;

            JSONArray stakeInfo = new JSONArray()
                    .put(new JSONObject()
                            .put("outcomeInfo",
                                    new JSONObject()
                                            .put("betId", betReference)
                                            .put("outcome", outcome)
                                            .put("outcomedate", expectedOutcomeTime)
                                            .put("payout", payout)
                                            .put("winnings", winnings)
                                            .put("withholdingTax", withholdingTax)
                                            .put("walletBalanceStake", walletBalanceStake)));

            Map<String, String> kraOutcomeResponse = KRAResultsDataTransmit(
                    new JSONObject()
                            .put("Request", new JSONObject()
                                    .put("hash", Utilities.sha256(hash))
                                    .put("header", new JSONObject()
                                            .put("operatorPin", kraConfigs.get("username"))
                                            .put("transactionDate", transactionDate)
                                            .put("noOfOutcomes", noOfStakes))
                                    .put("details", stakeInfo)),
                    kraToken,
                    kraConfigs.get("domains"));
            logger.info(Utilities.getLogPreString("KRAIntegrations")
                    + "handleKRAResults()"
                    + "|BetId:" + betId
                    + "|betReference:" + betReference
                    + "|KRADataTransmit::" + kraOutcomeResponse.toString());

            if (null == kraOutcomeResponse) {
                Thread.sleep(10000);
                return kraConfigs.get("domains").equals("api-test");
            }

            try {
                try (final java.sql.Connection dbConn
                        = db.WriteDataSource("trxn").getConnection();) {

                    dbConn.setAutoCommit(false);
                    try (final PreparedStatement ps = dbConn.prepareStatement(
                            "UPDATE compliance_data_transmission SET "
                            + "post_desc=?,post_date=NOW(),status=? "
                            + "WHERE id=? AND status=? AND post_date IS NULL LIMIT 1");) {
                        ps.setString(1, postDesc);
                        ps.setInt(2, 2000);
                        ps.setLong(3, (long) check.get("id"));
                        ps.setInt(4, 200);

                        int updateState = ps.executeUpdate();
                        if (updateState < 1) {
                            dbConn.rollback();
                            throw new SQLException("Failed to create compliance_data_transmission "
                                    + "for BetId:" + betId
                                    + " - BetReference:" + betReference);
                        }
                    } catch (Exception e) {
                        throw e;
                    }

                    long summaryId = 0;
                    try (final PreparedStatement ps = dbConn.prepareStatement(
                            "SELECT id FROM compliance_transaction_summary "
                            + "WHERE summary_date=DATE(NOW())");) {
                        try (ResultSet rs = ps.executeQuery()) {
                            while (rs.next()) {
                                summaryId = rs.getLong("id");
                            }
                        } catch (SQLException e) {
                            throw e;
                        }
                    } catch (Exception e) {
                        throw e;
                    }

                    if (summaryId < 1) {
                        try (final PreparedStatement ps = dbConn.prepareStatement(
                                "INSERT INTO compliance_transaction_summary"
                                + "(bets,payout,witholding_tax,summary_date"
                                + ",created_at) VALUES(?,?,DATE(NOW()),NOW())",
                                PreparedStatement.RETURN_GENERATED_KEYS);) {
                            ps.setInt(1, 1);
                            ps.setDouble(2, payout);
                            ps.setDouble(3, withholdingTax);
                            summaryId = ps.executeUpdate();
                            try (ResultSet rs = ps.getGeneratedKeys()) {
                                if (rs.next()) {
                                    summaryId = rs.getLong(1);
                                }
                            }

                            if (summaryId < 1) {
                                dbConn.rollback();
                                throw new SQLException("Failed to create compliance_transaction_summary "
                                        + "for BetId:" + betId
                                        + " - BetReference:" + betReference);
                            }
                        } catch (Exception e) {
                            throw e;
                        }
                    } else {
                        if (withholdingTax > 0) {
                            try (final PreparedStatement ps = dbConn.prepareStatement(
                                    "UPDATE compliance_transaction_summary SET "
                                    + "witholding_tax=witholding_tax+?,payout=payout+? "
                                    + "WHERE id=? LIMIT 1");) {
                                ps.setDouble(1, withholdingTax);
                                ps.setDouble(2, payout);
                                ps.setLong(3, summaryId);
                                int updateState = ps.executeUpdate();
                                if (updateState < 1) {
                                    dbConn.rollback();
                                    throw new SQLException("Failed to Update compliance_transaction_summary "
                                            + "for BetId:" + betId
                                            + " - BetReference:" + betReference);
                                }
                            } catch (Exception e) {
                                throw e;
                            }
                        }
                    }
                    dbConn.commit();

                    processed = true;
                } catch (SQLException e) {
                    throw e;
                }
            } catch (Exception e) {
                Queue.publishMessage(
                        new JSONObject()
                                .put("type", "RESULTS")
                                .put("response", kraOutcomeResponse)
                                .put("request", new JSONObject(message))
                                .toString(),
                        "KRA_FAILED",
                        "KRA_FAILED",
                        "KRA_FAILED",
                        null,
                        null);
                return true;
            }

            processed = true;
        } catch (Exception e) {
            logger.error(Utilities.getLogPreString("KRAIntegrations")
                    + "handleKRAResults()"
                    + "-" + queueName
                    + "|Took " + Utilities.CalculateTAT(tat) + " mSec(s)"
                    + "|Exception:", e);
            processed = false;
        }

        return processed;
    }

    /**
     * handleKRAStake
     *
     * @param body
     * @param queueName
     * @return
     */
    @SuppressWarnings({"UseSpecificCatch", "null", "unused"})
    private boolean handleKRAStake(byte[] body, String queueName) {
        Instant tat = Instant.now();
        boolean processed = false;
        String message = new String(body, StandardCharsets.UTF_8);
        try {
            logger.info(Utilities.getLogPreString("KRAIntegrations")
                    + "handleKRAStake()"
                    + "|Received"
                    + "-" + queueName
                    + "- Msg:" + message);

            KraStake kraStake;
            try {
                kraStake = new KraStake();
                JSONObject obj = new JSONObject(message);
                kraStake.setBetId(obj.getLong("bet_id"));
                kraStake.setBetType(obj.getString("bet_type"));
                kraStake.setBetReference(obj.getString("bet_reference"));
                kraStake.setMobileNumber(obj.getString("mobile_number"));
            } catch (Exception js) {
                logger.error(Utilities.getLogPreString("KRAIntegrations")
                        + "handleKRAStake()"
                        + "-" + queueName
                        + "|Took " + Utilities.CalculateTAT(tat) + " mSec(s)"
                        + "|Exception:" + js.getMessage());
                return true;
            }

            Map<String, Object> betInfo = null;
            try (final java.sql.Connection dbConn = db.ReadDataSource("bets").getConnection();) {
                if (kraStake.getBetType().equalsIgnoreCase("SPORTS")) {
                    betInfo = RabbitUtils.getSportsBetDetails(kraStake.getBetId(), dbConn);
                }
            } catch (Exception e) {
            }

            if (null == betInfo) {
                logger.error(Utilities.getLogPreString("KRAIntegrations")
                        + "handleKRAStake()"
                        + "|BetId:" + kraStake.getBetId()
                        + "|BetReference:" + kraStake.getBetReference()
                        + "|BetDetails NOT FOUND!!!");
                return true;
            }

            if (!Utilities.FindInIntegerArray(new int[]{0}, (int) betInfo.get("status"))
                    || !Utilities.FindInIntegerArray(basics.finalBetKRAStatus,
                            (int) betInfo.get("kraReport"))) {
                logger.error(Utilities.getLogPreString("KRAIntegrations")
                        + "handleKRAStake()"
                        + "|BetId:" + kraStake.getBetId()
                        + "|BetReference:" + kraStake.getBetReference()
                        + "|BetStatus:" + betInfo.get("status")
                        + "|kraReport:" + betInfo.get("kraReport")
                        + "|Bet Already sent to KRA!");
                return true;
            }

            Map<String, Object> check = checkBetTransmission(
                    kraStake.getBetReference(),
                    kraStake.getBetType(),
                    kraStake.getCustomerId());
            if (check != null) {
                if (Utilities.FindInIntegerArray(new int[]{200, 2000}, (int) check.get("status"))) {
                    logger.error(Utilities.getLogPreString("KRAIntegrations")
                            + "handleKRAStake()"
                            + "|BetId:" + kraStake.getBetId()
                            + "|BetReference:" + kraStake.getBetReference()
                            + "|Duplicate or stake already submitted!");
                    return true;
                }
            }

            Map<String, String> kraConfigs = null;

            String key = basics.KRASettingKey
                    .replace("{clientId}", betInfo.get("clientId").toString());
            String redisData = JedisUtils.selectOneData(key);
            if (!Utilities.isBlank(redisData)) {
                try {
                    kraConfigs = new JSONObject(redisData).toMap();
                } catch (JSONException e) {
                }
            }

            if (null == kraConfigs) {
                kraConfigs = getKraComplianceSettings((int) betInfo.get("clientId"));
                if (null == kraConfigs) {
                    logger.error(Utilities.getLogPreString("KRAIntegrations")
                            + "handleKRAStake()"
                            + "|BetId:" + kraStake.getBetId()
                            + "|BetReference:" + kraStake.getBetReference()
                            + "|Invalid KRA Configs!");
                    return false;
                }

                JedisUtils.saveData(key, new JSONObject(kraConfigs).toString(), 60 * 50);
            }

            logger.info(Utilities.getLogPreString("KRAIntegrations")
                    + "handleKRAStake()"
                    + "|BetId:" + kraStake.getBetId()
                    + "|BetReference:" + kraStake.getBetReference()
                    + "|PIN:" + kraConfigs.get("username"));

            key = basics.KRATokenKey.replace("{clientId}",
                    betInfo.get("clientId").toString());
            String kraToken = JedisUtils.selectOneData(key);
            if (Utilities.isBlank(kraToken)) {
                kraToken = generateBearerToken(
                        kraConfigs.get("username").toLowerCase(),
                        kraConfigs.get("password"),
                        kraConfigs.get("domains"));
                if (null == kraToken) {
                    logger.error(Utilities.getLogPreString("KRAIntegrations")
                            + "handleKRAStake()"
                            + "|BetId:" + kraStake.getBetId()
                            + "|BetReference:" + kraStake.getBetReference()
                            + "|PIN:" + kraConfigs.get("username")
                            + "|Failed to Generate Bearer Token!");
                    return kraConfigs.get("domains").equals("api-test");
                }

                JedisUtils.saveData(key, kraToken, 60 * 50);
            }

            kraStake.setCustomerId((long) betInfo.get("profileId"));
            Map<String, Double> walletInfo = RabbitUtils.GetWalletBalance(
                    db.ReadDataSource("profile"), kraStake.getCustomerId());

            int noOfStakes = 1;

            double stake = (double) betInfo.get("stake");
            double exciseTax = (double) betInfo.get("exciseTax");
            double walletBalanceStake = (walletInfo.get("balance") + walletInfo.get("bonus"));

            kraStake.setBetAmountAfterTax(stake - exciseTax);

            String transactionDate = DateFormatter(Utilities.now("yyyy-MM-dd HH:mm:ss"));
            String hash = kraConfigs.get("username")
                    + "" + transactionDate
                    + "" + noOfStakes;
            String desc = "PendingOutcome";

            LocalDateTime now = LocalDateTime.now();
            LocalDateTime plusOneDay = now.plusDays(1);
            String pattern = "yyyy-MM-dd'T'HH:mm:ss";
            String expectedOutcomeTime = plusOneDay.format(DateTimeFormatter.ofPattern(pattern));

            JSONArray stakeInfo = new JSONArray()
                    .put(new JSONObject()
                            .put("stakeInfo",
                                    new JSONObject()
                                            .put("betId", kraStake.getBetReference())
                                            .put("customerId", String
                                                    .valueOf(kraStake.getCustomerId()))
                                            .put("mobileNo", kraStake.getMobileNumber())
                                            .put("punterAmt", stake)
                                            .put("stakeAmt", kraStake.getBetAmountAfterTax())
                                            .put("desc", desc)
                                            .put("odds", betInfo.get("odds"))
                                            .put("stakeType", kraStake.getBetType())
                                            .put("dateOfStake", DateFormatter(
                                                    betInfo.get("createdAt").toString()))
                                            .put("expectedOutcomeTime", expectedOutcomeTime)
                                            .put("exciseAmt", exciseTax)
                                            .put("walletBalanceStake", walletBalanceStake)));

            Map<String, String> kraTransmitResponse = KRADataTransmit(
                    new JSONObject()
                            .put("Request", new JSONObject()
                                    .put("hash", Utilities.sha256(hash))
                                    .put("header", new JSONObject()
                                            .put("operatorPin", kraConfigs.get("username"))
                                            .put("transactionDate", transactionDate)
                                            .put("noOfStakes", noOfStakes))
                                    .put("details", stakeInfo)),
                    kraToken,
                    kraConfigs.get("domains"));
            logger.info(Utilities.getLogPreString("KRAIntegrations")
                    + "handleKRAStake()"
                    + "|BetId:" + kraStake.getBetId()
                    + "|BetReference:" + kraStake.getBetReference()
                    + "|KRADataTransmit::" + kraTransmitResponse.toString());

            if (null == kraTransmitResponse) {
                Thread.sleep(10000);
                return processed;
            }

            try {
                JSONObject eData = new JSONObject(kraTransmitResponse);
                eData.put("expectedOutcomeTime", expectedOutcomeTime);

                try (final java.sql.Connection dbConn = db.WriteDataSource("trxn").getConnection();) {
                    dbConn.setAutoCommit(false);
                    try (final PreparedStatement ps = dbConn.prepareStatement(
                            "INSERT INTO compliance_data_transmission"
                            + "(bet_id,bet_type,bet_amount,bet_odds,account_number"
                            + ",mobile_number,post_desc,extra_data,status,stake_post_date"
                            + ",created_at) VALUES(?,?,?,?,?,?,?,?,?,NOW(),NOW())",
                            PreparedStatement.RETURN_GENERATED_KEYS);) {
                        ps.setString(1, kraStake.getBetReference());
                        ps.setString(2, kraStake.getBetType());
                        ps.setDouble(3, stake);
                        ps.setObject(4, betInfo.get("odds"));
                        ps.setLong(5, kraStake.getCustomerId());
                        ps.setString(6, kraStake.getMobileNumber());
                        ps.setString(7, desc);
                        ps.setString(8, eData.toString());
                        ps.setString(9, kraTransmitResponse.get("code"));
                        long trxId = ps.executeUpdate();
                        try (ResultSet rs = ps.getGeneratedKeys()) {
                            if (rs.next()) {
                                trxId = rs.getLong(1);
                            }
                        }

                        if (trxId < 1) {
                            dbConn.rollback();
                            throw new SQLException("Failed to create compliance_data_transmission "
                                    + "for BetId:" + kraStake.getBetId()
                                    + " - BetReference:" + kraStake.getBetReference());
                        }
                    } catch (SQLException e) {
                        throw e;
                    }

                    long summaryId = 0;
                    try (final PreparedStatement ps = dbConn.prepareStatement(
                            "SELECT id FROM compliance_transaction_summary "
                            + "WHERE summary_date=DATE(NOW())");) {
                        try (ResultSet rs = ps.executeQuery()) {
                            while (rs.next()) {
                                summaryId = rs.getLong("id");
                            }
                        } catch (SQLException e) {
                            throw e;
                        }
                    } catch (Exception e) {
                        throw e;
                    }

                    if (summaryId < 1) {
                        try (final PreparedStatement ps = dbConn.prepareStatement(
                                "INSERT INTO compliance_transaction_summary"
                                + "(bets,stake,excirce_tax,witholding_tax,summary_date"
                                + ",created_at) VALUES(?,?,?,?,DATE(NOW()),NOW())",
                                PreparedStatement.RETURN_GENERATED_KEYS);) {
                            ps.setInt(1, 1);
                            ps.setDouble(2, stake);
                            ps.setDouble(3, exciseTax);
                            ps.setDouble(4, 0.0);
                            summaryId = ps.executeUpdate();
                            try (ResultSet rs = ps.getGeneratedKeys()) {
                                if (rs.next()) {
                                    summaryId = rs.getLong(1);
                                }
                            }

                            if (summaryId < 1) {
                                dbConn.rollback();
                                throw new SQLException("Failed to create compliance_transaction_summary "
                                        + "for BetId:" + kraStake.getBetId()
                                        + " - BetReference:" + kraStake.getBetReference());
                            }
                        } catch (Exception e) {
                            throw e;
                        }
                    } else {
                        try (final PreparedStatement ps = dbConn.prepareStatement(
                                "UPDATE compliance_transaction_summary SET "
                                + "excirce_tax=excirce_tax+?,stake=stake+?,bets=bets+1 "
                                + "WHERE id=? LIMIT 1");) {
                            ps.setDouble(1, exciseTax);
                            ps.setDouble(2, stake);
                            ps.setLong(3, summaryId);
                            int updateState = ps.executeUpdate();
                            if (updateState < 1) {
                                dbConn.rollback();
                                throw new SQLException("Failed to Update compliance_transaction_summary "
                                        + "for BetId:" + kraStake.getBetId()
                                        + " - BetReference:" + kraStake.getBetReference());
                            }
                        } catch (Exception e) {
                            throw e;
                        }
                    }
                    dbConn.commit();

                    processed = true;
                } catch (SQLIntegrityConstraintViolationException e) {
                    if (e.getErrorCode() == 1062 || e.getSQLState().startsWith("23")) {
                        logger.error(Utilities.getLogPreString("KRAIntegrations")
                                + "handleKRAStake()"
                                + "|BetId:" + kraStake.getBetId()
                                + "|BetReference:" + kraStake.getBetReference()
                                + "|Exception:" + e.getMessage());
                        return true;
                    }

                    throw e;
                }
            } catch (Exception e) {
                Queue.publishMessage(
                        new JSONObject()
                                .put("type", "STAKE")
                                .put("response", kraTransmitResponse)
                                .put("request", new JSONObject(message))
                                .toString(),
                        "KRA_FAILED",
                        "KRA_FAILED",
                        "KRA_FAILED",
                        null,
                        null);
                return true;
            }
        } catch (Exception e) {
            logger.error(Utilities.getLogPreString("KRAIntegrations")
                    + "handleKRAStake()"
                    + "-" + queueName
                    + "|Took " + Utilities.CalculateTAT(tat) + " mSec(s)"
                    + "|Exception:", e);
            processed = false;
        }

        return processed;
    }

    /**
     * checkBetTransmission
     *
     * @param betId
     * @param betType
     * @param profileId
     * @return
     */
    private Map<String, Object> checkBetTransmission(String betId, String betType, long mobileNumber) {
        Map<String, Object> result = null;
        try (final java.sql.Connection dbConn = db.ReadDataSource("trxn").getConnection();
                final PreparedStatement ps = dbConn.prepareStatement(
                        "SELECT id,status,post_desc,ifnull(post_date,'null') as post_date "
                        + "FROM compliance_data_transmission "
                        + "WHERE bet_id=? AND bet_type=? AND mobile_number=?");) {
            ps.setString(1, betId);
            ps.setString(2, betType);
            ps.setLong(3, mobileNumber);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    result = new HashMap<>();
                    result.put("id", rs.getLong("id"));
                    result.put("status", rs.getInt("status"));
                    result.put("postDate", rs.getString("post_date"));
                    result.put("postDesc", rs.getString("post_desc"));
                }
            } catch (SQLException e) {
                throw e;
            }
        } catch (Exception e) {
            result = null;
        }

        return result;
    }

    /**
     * GeneratePRN
     *
     * @param prnRequest
     * @param taxType
     * @return
     */
    @SuppressWarnings("unused")
    private Map<String, Object> generatePRN(JSONObject prnRequest, String taxType) {
        Map<String, Object> result = null;

        return result;
    }

    /**
     * generateBearerToken
     *
     * @param userName
     * @param password
     * @param mode
     * @return
     * @throws JSONException
     */
    private String generateBearerToken(String userName, String password, String mode) throws JSONException {
        String authToken = null;
        Map<String, String> postHeaders = new HashMap<>();

        Instant httpTime = Instant.now();
        Map<String, Object> httpResponse = ApiCalls.sendHttpJsonPostData(
                props.kraPrnAuthUrl().replace("{domain}", mode),
                new JSONObject()
                        .put("username", userName.toLowerCase())
                        .put("password", password)
                        .toString(),
                postHeaders, basics, logger, false);
        logger.info(Utilities.getLogPreString("KRAIntegrations")
                + "generateBearerToken()"
                + "|Took " + Utilities.CalculateTAT(httpTime) + " mSec(s)"
                + "|UserName:" + userName
                + "|SendHttpJsonPostData()" + httpResponse.toString());

        String str = httpResponse.get("response").toString();
        if (!Utilities.FindInIntegerArray(basics.succeessHttpStatuses,
                (int) httpResponse.get("statusCode")) || Utilities.isBlank(str)) {
            return authToken;
        }

        try {
            JSONObject obj = new JSONObject(str);
            if (obj.has("id_token")) {
                authToken = obj.optString("id_token", authToken);
            }
        } catch (JSONException e) {
        }

        return authToken;
    }

    /**
     * KRADataTransmit
     *
     * @param dataRequest
     * @param token
     * @param mode
     * @return
     */
    private Map<String, String> KRADataTransmit(JSONObject dataRequest, String token, String mode) {
        Map<String, String> result = null;
        Map<String, String> reqHeaders = new HashMap<>();
        reqHeaders.put("Authorization: Bearer ", token);

        Instant httpTime = Instant.now();
        Map<String, Object> httpResponse = ApiCalls.sendHttpJsonPostData(
                props.kraStakeDataTransmitUrl().replace("{domain}", mode),
                dataRequest.toString(),
                reqHeaders,
                basics, logger, false);
        logger.info(Utilities.getLogPreString("KRAIntegrations")
                + "KRADataTransmit()"
                + "|Took " + Utilities.CalculateTAT(httpTime) + " mSec(s)"
                + "|SendHttpJsonPostData()" + httpResponse.toString());
        String str = httpResponse.get("response").toString();
        if (!Utilities.FindInIntegerArray(basics.succeessHttpStatuses,
                (int) httpResponse.get("statusCode")) || Utilities.isBlank(str)) {
            return result;
        }

        try {
            JSONObject obj = new JSONObject(str);

            if (!obj.has("RESPONSE")) {
                result = new HashMap<>();
                result.put("code", "400");
                result.put("desc", "KRADataTransmit Failed. Empty RESPONSE Obj!");
                return result;
            }

            JSONObject transmitResult = obj.getJSONObject("RESPONSE");
            if (!transmitResult.has("RESULT")) {
                result = new HashMap<>();
                result.put("code", "400");
                result.put("desc", "KRADataTransmit Failed. Empty RESULT obj!");
                return result;
            }

            JSONObject transmitResultObj = transmitResult.getJSONObject("RESULT");
            int resultResponseCode = transmitResultObj.optInt("ResponseCode", 0);
            String resultResponseMessage = transmitResultObj.optString("Message", "Error");

            if (resultResponseCode != 1111) {
                result = new HashMap<>();
                result.put("code", "400");
                result.put("desc", "KRADataTransmit Failed. " + resultResponseMessage);

                return result;
            }

            result = new HashMap<>();
            result.put("code", "200");
            result.put("desc", "KRADataTransmit Success. " + resultResponseMessage);
        } catch (JSONException e) {
            result = new HashMap<>();
            result.put("code", "500");
            result.put("desc", "KRADataTransmit Failed. " + e.getMessage());
        }

        return result;
    }

    /**
     * KRAResultsDataTransmit
     *
     * @param dataRequest
     * @param token
     * @param mode
     * @return
     */
    private Map<String, String> KRAResultsDataTransmit(JSONObject dataRequest, String token, String mode) {
        Map<String, String> result = null;
        Map<String, String> reqHeaders = new HashMap<>();
        reqHeaders.put("Authorization: Bearer ", token);

        Instant httpTime = Instant.now();
        Map<String, Object> httpResponse = ApiCalls.sendHttpJsonPostData(
                props.kraStakeOutcomeTransmitUrl().replace("{domain}", mode),
                dataRequest.toString(),
                reqHeaders,
                basics, logger, false);
        logger.info(Utilities.getLogPreString("KRAIntegrations")
                + "KRAResultsDataTransmit()"
                + "|Took " + Utilities.CalculateTAT(httpTime) + " mSec(s)"
                + "|SendHttpJsonPostData()" + httpResponse.toString());
        String str = httpResponse.get("response").toString();
        if (!Utilities.FindInIntegerArray(basics.succeessHttpStatuses,
                (int) httpResponse.get("statusCode")) || Utilities.isBlank(str)) {
            return result;
        }

        try {
            JSONObject obj = new JSONObject(str);

            if (!obj.has("RESPONSE")) {
                result = new HashMap<>();
                result.put("code", "400");
                result.put("desc", "KRAResultsDataTransmit Failed. Empty RESPONSE Obj!");
                return result;
            }

            JSONObject transmitResult = obj.getJSONObject("RESPONSE");
            if (!transmitResult.has("RESULT")) {
                result = new HashMap<>();
                result.put("code", "400");
                result.put("desc", "KRAResultsDataTransmit Failed. Empty RESULT obj!");
                return result;
            }

            JSONObject transmitResultObj = transmitResult.getJSONObject("RESULT");
            int resultResponseCode = transmitResultObj.optInt("ResponseCode", 0);
            String resultResponseMessage = transmitResultObj.optString("Message", "Error");

            if (resultResponseCode != 1111) {
                result = new HashMap<>();
                result.put("code", "400");
                result.put("desc", "KRAResultsDataTransmit Failed. " + resultResponseMessage);

                return result;
            }

            result = new HashMap<>();
            result.put("code", "200");
            result.put("desc", "KRAResultsDataTransmit Success. " + resultResponseMessage);
        } catch (JSONException e) {
            result = new HashMap<>();
            result.put("code", "500");
            result.put("desc", "KRAResultsDataTransmit Failed. " + e.getMessage());
        }

        return result;
    }

    /**
     * DateFormatter
     *
     * @param dateTime
     * @return
     */
    private String DateFormatter(String dateTime) {
        try {
            ZonedDateTime zdt = ZonedDateTime.parse(dateTime);
            return zdt.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        } catch (DateTimeParseException e) {
            try {
                LocalDateTime ldt = LocalDateTime.parse(dateTime,
                        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                return ldt.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            } catch (Exception ex) {
                throw new IllegalArgumentException("Invalid date format: " + dateTime);
            }
        }
    }

    /**
     * getKraComplianceSettings
     *
     * @param clientId
     * @return
     */
    private Map<String, String> getKraComplianceSettings(int clientId) {
        Map<String, String> conf = null;
        try (final java.sql.Connection dbConn = db.ReadDataSource("trxn").getConnection();
                final PreparedStatement ps = dbConn.prepareStatement(
                        "SELECT id,username,password,email,mode FROM compliance_settings "
                        + "WHERE status=1 AND client_id=?");) {
            ps.setInt(1, clientId);
            try (ResultSet rs = ps.executeQuery()) {
                while (rs.next()) {
                    conf = new HashMap<>();
                    conf.put("id", rs.getString("id"));
                    conf.put("username", rs.getString("username"));
                    conf.put("password", rs.getString("password"));
                    conf.put("email", rs.getString("email"));
                    conf.put("domains", rs.getInt("mode") == 1 ? "api-test" : "api-prd");
                }
            } catch (SQLException se) {

            }
        } catch (Exception ex) {

        }

        return conf;
    }
}

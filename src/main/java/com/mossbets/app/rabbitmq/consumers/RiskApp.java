package com.mossbets.app.rabbitmq.consumers;

import com.mossbets.app.rabbitmq.models.RiskApprovals;
import com.mossbets.app.rabbitmq.utils.RabbitUtils;
import com.mossbets.app.resources.DB;
import com.mossbets.app.utils.MailUtils;
import com.mossbets.app.utils.Queue;
import com.mossbets.app.utils.Utilities;
import com.mossbets.app.utils.props.Basics;
import com.mossbets.app.utils.props.Props;
import com.rabbitmq.client.AMQP;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;
import com.rabbitmq.client.DefaultConsumer;
import com.rabbitmq.client.Envelope;
import io.quarkus.runtime.ShutdownEvent;
import io.quarkus.runtime.StartupEvent;
import io.quarkus.scheduler.Scheduled;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.event.Observes;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.Instant;
import java.util.LinkedHashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import org.codehaus.jettison.json.JSONObject;
import org.jboss.logging.Logger;

@ApplicationScoped
public class RiskApp {

    @Inject
    Props props;

    @Inject
    Basics basics = new Basics();

    @Inject
    DB db;

    @Inject
    Logger logger;

    @Inject
    private Queue Queue;

    @Inject
    private MailUtils MailUtils;

    @Inject
    @Named("refundExecutor")
    ExecutorService es;

    @Inject
    ScheduledExecutorService scheduler;

    private Connection connection;
    private int reconnectAttempt = 0;
    private final int maxBackoffSeconds = 60;
    private volatile boolean running = true;

    void onStart(@Observes StartupEvent ev) {
        logger.info(Utilities.getLogPreString("RiskApp")
                + "onStart()"
                + "|Starting RabbitMQ Consumer...");
        scheduleReconnect(0);
    }

    void onStop(@Observes ShutdownEvent event) {
        logger.info(Utilities.getLogPreString("RiskApp")
                + "onStop()"
                + "|Application shutting down...");
        cleanup();
    }

    @PostConstruct
    void startOnAppBoot() {
        scheduleReconnect(0); // ✅ triggers startup
    }

    /**
     * scheduleReconnect
     *
     * @param delaySeconds
     */
    private void scheduleReconnect(int delaySeconds) {
        scheduler.schedule(this::connectWithBackoff, delaySeconds, TimeUnit.SECONDS);
    }

    /**
     * connectWithBackoff
     */
    private void connectWithBackoff() {
        if (!running) {
            return;
        }

        try {
            connectAndStartConsuming();
            logger.info(Utilities.getLogPreString("RiskApp")
                    + "connectWithBackoff()"
                    + "|RabbitMQ Consumer connected and consuming....");
            reconnectAttempt = 0; // reset on success
        } catch (Exception e) {
            reconnectAttempt++;
            int backoff = calculateBackoff(reconnectAttempt);
            logger.errorf(Utilities.getLogPreString("RiskApp")
                    + "connectWithBackoff()"
                    + "|Reconnect failed (attempt #%d). Retrying in %ds...",
                    reconnectAttempt, backoff);
            scheduleReconnect(backoff);
        }
    }

    /**
     * calculateBackoff
     *
     * @param attempt
     * @return
     */
    private int calculateBackoff(int attempt) {
        int base = (int) Math.min(Math.pow(2, attempt), maxBackoffSeconds);
        int jitter = ThreadLocalRandom.current().nextInt(0, 5);
        return base + jitter; // Exponential + jitter
    }

    /**
     * createFactory
     *
     * @return
     */
    private ConnectionFactory createFactory() {
        ConnectionFactory factory = new ConnectionFactory();
        factory.setHost(props.rabbitMqHost());
        factory.setPort(props.rabbitMqPort());
        factory.setVirtualHost(props.rabbitMqVhost());
        factory.setUsername(props.rabbitMqUsername());
        factory.setPassword(props.rabbitMqPassword());
        factory.setTopologyRecoveryEnabled(props.rabbitMqConRecovery());
        factory.setAutomaticRecoveryEnabled(props.rabbitMqTopologyRecovery());
        factory.setNetworkRecoveryInterval(props.rabbitMqNetworkRecovery());
        return factory;
    }

    /**
     * connectAndStartConsuming
     *
     * @throws Exception
     */
    private void connectAndStartConsuming() throws Exception {
        connection = createFactory().newConnection();
        connection.addShutdownListener(cause -> {
            logger.info(Utilities.getLogPreString("RiskApp")
                    + "connectAndStartConsuming()"
                    + "|Connection shutdown: " + cause.getMessage());
            if (running) {
                scheduleReconnect(0); // retry again
            }
        });

        for (String queue : props.RiskApprovalQueues()) {
            String queueName = props.rabbitMqPrefix().concat("_" + queue + "_QUEUE");
            String exchange = props.rabbitMqPrefix().concat("_" + queue + "_EXCHANGE");
            String route = props.rabbitMqPrefix().concat("_" + queue + "_ROUTE");

            Channel channel = connection.createChannel();
            channel.queueDeclare(queueName, true, false, false, null);
            channel.exchangeDeclare(exchange, "direct", true, false, null);
            channel.queueBind(queueName, exchange, route);
            channel.basicQos(props.rabbitMqPrefetchCount());
            channel.basicConsume(queueName, false, new DefaultConsumer(channel) {
                @Override
                @SuppressWarnings("UseSpecificCatch")
                public void handleDelivery(String consumerTag, Envelope envelope,
                        AMQP.BasicProperties properties, byte[] body) {
                    long deliveryTag = envelope.getDeliveryTag();
                    try {
                        es.submit(() -> handleMessage(body, queueName, channel, deliveryTag));
                    } catch (Exception ex) {
                        logger.error(Utilities.getLogPreString("RiskApp")
                                + "connectAndStartConsuming()"
                                + "|Queue " + queueName
                                + "|ConsumerTag:" + consumerTag
                                + "|Exception: " + ex.getMessage());
                        rejectAck(channel, deliveryTag, true);
                    }
                }
            });

            logger.infof(Utilities.getLogPreString("RiskApp")
                    + "connectAndStartConsuming()"
                    + "|Queue [%s]"
                    + "|Exchange [%s]"
                    + "|Route [%s]", queueName, exchange, route);
        }
    }

    /**
     * handleMessage
     *
     * @param body
     * @param queueName
     * @param channel
     * @param deliveryTag
     */
    @SuppressWarnings("UseSpecificCatch")
    private void handleMessage(byte[] body, String queueName, Channel channel, long deliveryTag) {
        Instant tat = Instant.now();
        try {
            if (handleRiskApproval(body, queueName)) {
                successAck(channel, deliveryTag);
            } else {
                rejectAck(channel, deliveryTag, true);
            }

            logger.info(Utilities.getLogPreString("RiskApp")
                    + "handleMessage()"
                    + "-" + queueName
                    + "|Took " + Utilities.CalculateTAT(tat) + " mSec(s)"
                    + "|Tag:" + deliveryTag);
        } catch (Exception e) {
            rejectAck(channel, deliveryTag, true);

            logger.error(Utilities.getLogPreString("RiskApp")
                    + "handleMessage()"
                    + "-" + queueName
                    + "|Took " + Utilities.CalculateTAT(tat) + " mSec(s)"
                    + "|Exception:" + e.getMessage());
        }
    }

    /**
     * handleDownload
     *
     * @param body
     * @param queueName
     * @return
     */
    @SuppressWarnings("UseSpecificCatch")
    private boolean handleRiskApproval(byte[] body, String queueName) {

        boolean processed = false;
        String message = new String(body, StandardCharsets.UTF_8);
        try {
            logger.info(Utilities.getLogPreString("RiskApp")
                    + "handleRiskApproval()"
                    + "|Received"
                    + "-" + queueName
                    + "- Msg:" + message);

            RiskApprovals risk = new RiskApprovals();
            try {
                JSONObject obj = new JSONObject(message);
                risk.setId(obj.getLong("id"));
                risk.setDate(obj.getString("date"));
                risk.setSignature(obj.getString("signature"));
            } catch (Exception js) {
                logger.error(Utilities.getLogPreString("RiskApp")
                        + "handleRiskApproval()"
                        + "-" + queueName
                        + "|Exception:" + js.getMessage());
                return true;
            }

            Map<String, Object> pendingTsk = null;
            Map<String, Object> userMap = null;
            try (final java.sql.Connection dbConn = db.ReadDataSource("trxn").getConnection();) {
                pendingTsk = RabbitUtils.getPendingApprovals(risk.getId(), dbConn);
                if (null != pendingTsk) {
                    userMap = RabbitUtils.getUserDetails((int) pendingTsk.get("approved_by"), dbConn);
                }
            } catch (Exception e) {
            }

            if (null == pendingTsk || null == userMap) {
                logger.info(Utilities.getLogPreString("RiskApp")
                        + "handleRiskApproval()"
                        + "|TransactionId:" + risk.getId()
                        + "|Transaction query returned empty results!");
                return true;
            }

            if (!risk.getSignature().equalsIgnoreCase(
                    Utilities.CreateMd5(risk.getId()
                            + "" + pendingTsk.get("approved_by")))) {
                logger.warn(Utilities.getLogPreString("RiskApp")
                        + "handleRiskApproval()"
                        + "|TransactionId:" + risk.getId()
                        + "|Transaction Signature is Invalid!");
                return true;
            }

            logger.info(Utilities.getLogPreString("RiskApp")
                    + "handleRiskApproval()"
                    + "|TransactionId:" + risk.getId()
                    + "|USerDetails: " + userMap.toString());

            long profileId = (long) pendingTsk.get("profileId");
            try (final java.sql.Connection dbT = db.WriteDataSource("trxn").getConnection();
                    final java.sql.Connection dbP = db.WriteDataSource("profile").getConnection();) {

                dbT.setAutoCommit(false);
                dbP.setAutoCommit(false);

                long accountId = 0;
                try (final PreparedStatement ps = dbP.prepareStatement(
                        "SELECT id FROM profile_balance WHERE profile_id=?");) {
                    ps.setLong(1, profileId);
                    try (ResultSet rs = ps.executeQuery()) {
                        while (rs.next()) {
                            accountId = rs.getLong("id");
                        }
                    } catch (SQLException se) {
                        throw se;
                    }

                    if (accountId < 1) {
                        try (final PreparedStatement psCreate = dbP.prepareStatement(
                                "INSERT INTO profile_balance(profile_id,currency"
                                + ",balance,bonus,created_at) VALUES(?,?,?,?,NOW())",
                                PreparedStatement.RETURN_GENERATED_KEYS);) {
                            psCreate.setLong(1, profileId);
                            psCreate.setString(2, pendingTsk.get("currency").toString());
                            psCreate.setDouble(3, (double) pendingTsk.get("amount"));
                            psCreate.setDouble(4, 0);
                            accountId = psCreate.executeUpdate();
                            try (ResultSet rs = psCreate.getGeneratedKeys()) {
                                if (rs.next()) {
                                    accountId = rs.getLong(1);
                                }
                            }

                            if (accountId < 1) {
                                throw new SQLException("Profile Wallet creation failed "
                                        + "|Q:" + queueName
                                        + "|TransactionId:" + risk.getId());
                            }
                        } catch (SQLException e) {
                            throw e;
                        }
                    } else {
                        try (final PreparedStatement psUpdate
                                = dbP.prepareStatement("UPDATE profile_balance "
                                        + "SET balance=balance+?,bonus=bonus+? "
                                        + "WHERE id=? LIMIT 1");) {
                            psUpdate.setDouble(1, (double) pendingTsk.get("amount"));
                            psUpdate.setDouble(2, 0);
                            psUpdate.setLong(3, accountId);
                            int updateState = psUpdate.executeUpdate();
                            if (updateState < 1) {
                                throw new SQLException("Profile Wallet Update failed "
                                        + "|Q:" + queueName
                                        + "|TransactionId:" + risk.getId());
                            }
                        } catch (SQLException e) {
                            throw e;
                        }
                    }
                } catch (SQLException se) {
                    dbP.rollback();
                    throw se;
                }

                try (final PreparedStatement psUpdate = dbT.prepareStatement(
                        "UPDATE transaction_pending_approval "
                        + "SET completed_on=NOW() WHERE id=? LIMIT 1");) {
                    psUpdate.setLong(1, risk.getId());
                    int updateState = psUpdate.executeUpdate();
                    if (updateState < 1) {
                        throw new SQLException("Pending Risk Approvals Update failed "
                                + "|Q:" + queueName
                                + "|TransactionId:" + risk.getId());
                    }
                } catch (SQLException e) {
                    dbP.rollback();
                    dbT.rollback();
                    throw e;
                }

                dbT.commit();
                dbP.commit();

                processed = true;
            } catch (Exception e) {
                throw e;
            }

            logger.info(Utilities.getLogPreString("RiskApp")
                    + "handleRiskApproval()"
                    + "|TransactionId:" + risk.getId()
                    + "|pId: " + profileId
                    + "|Approved: " + pendingTsk.get("currency") + "." + pendingTsk.get("amount"));

            RabbitUtils.LogUserActions(
                    db.WriteDataSource("profile"),
                    (long) pendingTsk.get("approved_by"),
                    "handleRiskApproval",
                    "handleRiskApproval()"
                    + "|TransactionId:" + risk.getId()
                    + "|pId: " + profileId
                    + "|Approved: " + pendingTsk.get("currency")
                    + "." + pendingTsk.get("amount"));

            String emailSubject = "Risk Approvals";

            Set<String> emailList = new LinkedHashSet<>();
            emailList.add("<EMAIL>");
            emailList.add("<EMAIL>");
            emailList.add("<EMAIL>");
            emailList.add("<EMAIL>");
            emailList.add(userMap.get("email").toString());

            MailUtils.SendMail(
                    String.join(",", emailList),
                    userMap.get("name").toString(),
                    new StringBuilder()
                            .append("<h4>Risk Approvals</h4>")
                            .append("<span>ID:</span>")
                            .append(risk.getId())
                            .append("<span>Source:</span>")
                            .append(pendingTsk.get("source"))
                            .append("<br/><span>Reason:</span>")
                            .append(pendingTsk.get("reason"))
                            .append("<br/><span>Desc:</span>")
                            .append(pendingTsk.get("description"))
                            .append("<br/><span>Amount:</span>")
                            .append(pendingTsk.get("currency"))
                            .append(".")
                            .append(pendingTsk.get("amount"))
                            .append("<br/><span>Date:</span>")
                            .append(risk.getDate()).toString(),
                    emailSubject, null);

        } catch (Exception e) {
            logger.error(Utilities.getLogPreString("RiskApp")
                    + "handleRiskApproval()"
                    + "-" + queueName
                    + "|Exception:", e);
            processed = false;
        }

        return processed;
    }

    /**
     * cleanup
     */
    @PreDestroy
    @SuppressWarnings("UseSpecificCatch")
    void cleanup() {
        logger.info(Utilities.getLogPreString("RiskApp")
                + "cleanup()"
                + "|Shutting DOWN RabbitMQ Consumer gracefully...");
        running = false;
        try {
            if (connection != null && connection.isOpen()) {
                connection.close();
            }

            logger.info(Utilities.getLogPreString("RiskApp")
                    + "cleanup()"
                    + "|RabbitMQ Connection closed");
        } catch (Exception e) {
            logger.fatal(Utilities.getLogPreString("RiskApp")
                    + "cleanup()"
                    + "|Exception", e);
        }
    }

    /**
     * isHealthy
     *
     * @return
     */
    public boolean isHealthy() {
        return connection != null && connection.isOpen();
    }

    /**
     * reconnectWithDelay
     */
    private void reconnectWithDelay() {
        reconnectAttempt++;
        int delay = calculateBackoff(reconnectAttempt);

        logger.warnf(Utilities.getLogPreString("RiskApp")
                + "reconnectWithDelay()"
                + "|Scheduling Reconnection in %d seconds (attempt #%d)...",
                delay, reconnectAttempt);

        scheduler.schedule(this::connectWithBackoff, delay, TimeUnit.SECONDS);
    }

    @Scheduled(every = "10m", concurrentExecution = Scheduled.ConcurrentExecution.SKIP)
    public void checkIfAppIsRunning() {
        if (isHealthy()) {
            logger.info(Utilities.getLogPreString("RiskApp")
                    + "AppChecks() => ALIVE");
        } else if (running) {
            logger.info(Utilities.getLogPreString("RiskApp")
                    + "AppChecks()"
                    + "|RabbitMQ Connection is DOWN. Attempting to reconnect...");
            reconnectWithDelay();
        }
    }

    /**
     * acknowledge
     *
     * @param channel
     * @param deliveryTag
     * @throws IOException
     */
    public void successAck(Channel channel, long deliveryTag) throws IOException {
        try {
            if (channel != null && channel.isOpen()) {
                channel.basicAck(deliveryTag, false);
                logger.infof(Utilities.getLogPreString("RiskApp")
                        + "successAck()"
                        + "|ACK [%s] tag:%d", "Success", deliveryTag);
            }
        } catch (IOException e) {
            throw e;
        }
    }

    /**
     *
     * @param channel
     * @param deliveryTag
     * @param requeue
     */
    public void rejectAck(Channel channel, long deliveryTag, boolean requeue) {
        try {
            if (channel != null && channel.isOpen()) {
                channel.basicReject(deliveryTag, requeue);
                logger.infof(Utilities.getLogPreString("RiskApp")
                        + "rejectAck()"
                        + "|ACK [%s] tag:%d", "Reject", deliveryTag);
            }
        } catch (IOException e) {
            logger.error(Utilities.getLogPreString("RiskApp")
                    + "rejectAck()"
                    + "IOException:" + e.getMessage()
                    + "|ACK Failed tag:" + deliveryTag);
        }
    }
}

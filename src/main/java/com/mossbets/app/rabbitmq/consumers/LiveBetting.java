package com.mossbets.app.rabbitmq.consumers;

import com.mossbets.app.rabbitmq.models.Abbly;
import com.mossbets.app.rabbitmq.models.LiveBets;
import com.mossbets.app.rabbitmq.utils.RabbitUtils;
import com.mossbets.app.resources.DB;
import com.mossbets.app.utils.ApiCalls;
import com.mossbets.app.utils.Queue;
import com.mossbets.app.utils.Utilities;
import static com.mossbets.app.utils.Utilities.GetBFRAuthToken;
import com.mossbets.app.utils.props.Basics;
import com.mossbets.app.utils.props.Props;
import com.rabbitmq.client.*;
import io.quarkus.runtime.ShutdownEvent;
import io.quarkus.runtime.StartupEvent;
import io.quarkus.scheduler.Scheduled;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.event.Observes;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.Instant;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import org.codehaus.jettison.json.JSONArray;
import org.codehaus.jettison.json.JSONException;
import org.codehaus.jettison.json.JSONObject;
import org.jboss.logging.Logger;

@ApplicationScoped
public class LiveBetting {

    @Inject
    Props props;

    @Inject
    Basics basics = new Basics();

    @Inject
    DB db;

    @Inject
    Logger logger;

    @Inject
    private Queue Queue;

    @Inject
    @Named("queueExecutor")
    ExecutorService es;

    @Inject
    ScheduledExecutorService scheduler;

    private Connection connection;
    private int reconnectAttempt = 0;
    private final int maxBackoffSeconds = 60;
    private volatile boolean running = true;

    void onStart(@Observes StartupEvent ev) {
        logger.info(Utilities.getLogPreString("LiveBetting")
                + "onStart()"
                + "|Starting RabbitMQ Consumer...");
        scheduleReconnect(0);
    }

    void onStop(@Observes ShutdownEvent event) {
        logger.info(Utilities.getLogPreString("LiveBetting")
                + "onStop()"
                + "|Application shutting down...");
        cleanup();
    }

    @PostConstruct
    void startOnAppBoot() {
        scheduleReconnect(0); // ✅ triggers startup
    }

    /**
     * scheduleReconnect
     *
     * @param delaySeconds
     */
    private void scheduleReconnect(int delaySeconds) {
        scheduler.schedule(this::connectWithBackoff, delaySeconds, TimeUnit.SECONDS);
    }

    /**
     * connectWithBackoff
     */
    private void connectWithBackoff() {
        if (!running) {
            return;
        }

        try {
            connectAndStartConsuming();
            logger.info(Utilities.getLogPreString("LiveBetting")
                    + "connectWithBackoff()"
                    + "|RabbitMQ Consumer connected and consuming....");
            reconnectAttempt = 0; // reset on success
        } catch (Exception e) {
            reconnectAttempt++;
            int backoff = calculateBackoff(reconnectAttempt);
            logger.errorf(Utilities.getLogPreString("LiveBetting")
                    + "connectWithBackoff()"
                    + "|Reconnect failed (attempt #%d). Retrying in %ds...",
                    reconnectAttempt, backoff);
            scheduleReconnect(backoff);
        }
    }

    /**
     * calculateBackoff
     *
     * @param attempt
     * @return
     */
    private int calculateBackoff(int attempt) {
        int base = (int) Math.min(Math.pow(2, attempt), maxBackoffSeconds);
        int jitter = ThreadLocalRandom.current().nextInt(0, 5);
        return base + jitter; // Exponential + jitter
    }

    /**
     * createFactory
     *
     * @return
     */
    private ConnectionFactory createFactory() {
        ConnectionFactory factory = new ConnectionFactory();
        factory.setHost(props.rabbitMqHost());
        factory.setPort(props.rabbitMqPort());
        factory.setVirtualHost(props.rabbitMqVhost());
        factory.setUsername(props.rabbitMqUsername());
        factory.setPassword(props.rabbitMqPassword());
        factory.setTopologyRecoveryEnabled(props.rabbitMqConRecovery());
        factory.setAutomaticRecoveryEnabled(props.rabbitMqTopologyRecovery());
        factory.setNetworkRecoveryInterval(props.rabbitMqNetworkRecovery());
        return factory;
    }

    /**
     * connectAndStartConsuming
     *
     * @throws Exception
     */
    private void connectAndStartConsuming() throws Exception {
        connection = createFactory().newConnection();
        connection.addShutdownListener(cause -> {
            logger.info(Utilities.getLogPreString("LiveBetting")
                    + "connectAndStartConsuming()"
                    + "|Connection shutdown: " + cause.getMessage());
            if (running) {
                scheduleReconnect(0); // retry again
            }
        });

        for (String queue : props.LiveBetQueues()) {
            String queueName = props.rabbitMqPrefix().concat("_" + queue + "_QUEUE");
            String exchange = props.rabbitMqPrefix().concat("_" + queue + "_EXCHANGE");
            String route = props.rabbitMqPrefix().concat("_" + queue + "_ROUTE");

            Channel channel = connection.createChannel();
            channel.queueDeclare(queueName, true, false, false, null);
            channel.exchangeDeclare(exchange, "direct", true, false, null);
            channel.queueBind(queueName, exchange, route);
            channel.basicQos(props.rabbitMqPrefetchCount());
            channel.basicConsume(queueName, false, new DefaultConsumer(channel) {
                @Override
                @SuppressWarnings("UseSpecificCatch")
                public void handleDelivery(String consumerTag, Envelope envelope,
                        AMQP.BasicProperties properties, byte[] body) {
                    long deliveryTag = envelope.getDeliveryTag();
                    try {
                        es.submit(() -> handleMessage(body, queueName, channel, deliveryTag));
                    } catch (Exception ex) {
                        logger.error(Utilities.getLogPreString("LiveBetting")
                                + "connectAndStartConsuming()"
                                + "|Queue " + queueName
                                + "|ConsumerTag:" + consumerTag
                                + "|Exception: " + ex.getMessage());
                        rejectAck(channel, deliveryTag, true);
                    }
                }
            });

            logger.infof(Utilities.getLogPreString("LiveBetting")
                    + "connectAndStartConsuming()"
                    + "|Queue [%s]"
                    + "|Exchange [%s]"
                    + "|Route [%s]", queueName, exchange, route);
        }
    }

    /**
     * isHealthy
     *
     * @return
     */
    public boolean isHealthy() {
        return connection != null && connection.isOpen();
    }

    /**
     * reconnectWithDelay
     */
    private void reconnectWithDelay() {
        reconnectAttempt++;
        int delay = calculateBackoff(reconnectAttempt);

        logger.warnf(Utilities.getLogPreString("LiveBetting")
                + "reconnectWithDelay()"
                + "|Scheduling Reconnection in %d seconds (attempt #%d)...",
                delay, reconnectAttempt);

        scheduler.schedule(this::connectWithBackoff, delay, TimeUnit.SECONDS);
    }

    @Scheduled(every = "10m", concurrentExecution = Scheduled.ConcurrentExecution.SKIP)
    public void checkIfAppIsRunning() {
        if (isHealthy()) {
            logger.info(Utilities.getLogPreString("LiveBetting")
                    + "AppChecks() => ALIVE");
        } else if (running) {
            logger.info(Utilities.getLogPreString("LiveBetting")
                    + "AppChecks()"
                    + "|RabbitMQ Connection is DOWN. Attempting to reconnect...");
            reconnectWithDelay();
        }
    }

    /**
     * acknowledge
     *
     * @param channel
     * @param deliveryTag
     * @throws IOException
     */
    public void successAck(Channel channel, long deliveryTag) throws IOException {
        try {
            if (channel != null && channel.isOpen()) {
                channel.basicAck(deliveryTag, false);
                logger.infof(Utilities.getLogPreString("LiveBetting")
                        + "successAck()"
                        + "|ACK [%s] tag:%d", "Success", deliveryTag);
            }
        } catch (IOException e) {
            throw e;
        }
    }

    /**
     *
     * @param channel
     * @param deliveryTag
     * @param requeue
     */
    public void rejectAck(Channel channel, long deliveryTag, boolean requeue) {
        try {
            if (channel != null && channel.isOpen()) {
                channel.basicReject(deliveryTag, requeue);
                logger.infof(Utilities.getLogPreString("LiveBetting")
                        + "rejectAck()"
                        + "|ACK [%s] tag:%d", "Reject", deliveryTag);
            }
        } catch (IOException e) {
            logger.error(Utilities.getLogPreString("LiveBetting")
                    + "rejectAck()"
                    + "IOException:" + e.getMessage()
                    + "|ACK Failed tag:" + deliveryTag);
        }
    }

    /**
     * handleMessage
     *
     * @param body
     * @param queueName
     * @param channel
     * @param deliveryTag
     */
    @SuppressWarnings("UseSpecificCatch")
    private void handleMessage(byte[] body, String queueName, Channel channel, long deliveryTag) {
        try {
            if (queueName.contains("ABLY_RESPONSE")) {
                if (handleAbblyResponse(body, queueName)) {
                    successAck(channel, deliveryTag);
                } else {
                    rejectAck(channel, deliveryTag, true);
                }
                return;
            }

            if (handleLiveBets(body, queueName)) {
                successAck(channel, deliveryTag);
            } else {
                rejectAck(channel, deliveryTag, true);
            }
        } catch (Exception e) {
            rejectAck(channel, deliveryTag, true);

            logger.error(Utilities.getLogPreString("LiveBetting")
                    + "handleMessage()"
                    + "-" + queueName
                    + "|Exception:" + e.getMessage());
        }
    }

    /**
     * handleLiveBets
     *
     * @param body
     * @param queueName
     * @return
     */
    @SuppressWarnings("UseSpecificCatch")
    private boolean handleLiveBets(byte[] body, String queueName) {
        Instant tat = Instant.now();
        boolean processed = false;
        String message = new String(body, StandardCharsets.UTF_8);
        try {
            logger.info(Utilities.getLogPreString("LiveBetting")
                    + "handleLiveBets()"
                    + "|Received"
                    + "-" + queueName
                    + "- Msg:" + message);

            LiveBets lBets = new LiveBets();
            try {
                JSONObject obj = new JSONObject(message);
                lBets.setBetId(obj.getLong("betId"));
                lBets.setProfileId(obj.getLong("playerId"));
                lBets.setTotalStake(obj.getDouble("totalStake"));
                lBets.setCurrencyCode(obj.getString("currencyCode"));
                lBets.setPriceChangeRule(obj.getString("priceChangeRule"));
                lBets.setBetLegs(obj.getJSONArray("legs"));
            } catch (Exception js) {
                logger.error(Utilities.getLogPreString("LiveBetting")
                        + "handleLiveBets()"
                        + "-" + queueName
                        + "|Took " + Utilities.CalculateTAT(tat) + " mSec(s)"
                        + "|Exception:" + js.getMessage());
                return true;
            }

            Map<String, Object> betInfo = null;
            try (final java.sql.Connection dbConn = db.ReadDataSource("bets").getConnection();) {
                betInfo = RabbitUtils.getSportsBetDetails(lBets.getBetId(), dbConn);
            } catch (Exception e) {
            }

            if (null == betInfo) {
                logger.info(Utilities.getLogPreString("LiveBetting")
                        + "handleLiveBets()"
                        + "|BetId:" + lBets.getBetId()
                        + "|Bet NOT FOUND!!!");
                return true;
            }

            if (Utilities.FindInIntegerArray(basics.finalBetStatus, (int) betInfo.get("status"))) {
                if (Utilities.FindInIntegerArray(basics.finalBetRiskStatus,
                        (int) betInfo.get("riskState"))) {
                    logger.info(Utilities.getLogPreString("LiveBetting")
                            + "handleLiveBets()"
                            + "|BetId:" + lBets.getBetId()
                            + "|Bet Already processed!!!");
                    return true;
                }
            }

            Map<String, String> httpHeader = new HashMap<>();
            httpHeader.put("User-Agent", basics.UA);
            httpHeader.put("X-Organization-Id", "Mossbets/v1");

            Instant httpTime = Instant.now();
            Map<String, Object> httpResponse = ApiCalls.sendHttpJsonPostData(
                    props.geniusRiskUrl(), message, httpHeader,
                    basics, logger, false);
            logger.info(Utilities.getLogPreString("ApiCalls")
                    + "handleLiveBets()"
                    + "|Took " + Utilities.CalculateTAT(httpTime) + " mSec(s)"
                    + "|URL:" + props.geniusRiskUrl()
                    + "|BetId:" + lBets.getBetId()
                    + "|SendHttpJsonPostData()" + httpResponse.toString());

            processed = Utilities.FindInIntegerArray(basics.succeessHttpStatuses,
                    (int) httpResponse.get("statusCode"));
            if (!processed) {
                JSONArray legs = lBets.getBetLegs();
                JSONArray BetAssessmentResultLegs = new JSONArray();
                for (int i = 0; i < legs.length(); i++) {
                    JSONObject leg = legs.getJSONObject(i);
                    leg.put("SelectionId", leg.getString("selectionId"));
                    leg.put("Price", leg.getDouble("price"));
                    leg.put("SelectionStatus", "Closed");
                    leg.put("RejectReason", new JSONObject()
                            .put("ReasonCode", "HTTP Code:" + httpResponse.get("statusCode"))
                            .put("ReasonMessage", "TAT:" + httpResponse.get("tat")
                                    + " Invalid Risk API response"));
                    BetAssessmentResultLegs.put(leg);
                }

                int rand = Utilities.GetRandomInt(0, 1);
                String abbltQueue = "ABLY_RESPONSE";
                if (rand > 0) {
                    abbltQueue += "_" + rand;
                }

                ZonedDateTime issuedAt = ZonedDateTime.now(ZoneOffset.UTC);

                processed = Queue
                        .publishMessage(
                                new JSONObject()
                                        .put("timestamp", issuedAt.toInstant().getEpochSecond())
                                        .put("IsBetAllowed", false)
                                        .put("BetId", lBets.getBetId())
                                        .put("playerId", lBets.getProfileId())
                                        .put("BetAssessmentResult", "BetRejected")
                                        .put("maxAllowedStake", 0)
                                        .put("Legs", BetAssessmentResultLegs)
                                        .toString(),
                                abbltQueue,
                                abbltQueue,
                                abbltQueue,
                                null,
                                null);

            }
        } catch (Exception e) {
            logger.error(Utilities.getLogPreString("LiveBetting")
                    + "handleLiveBets()"
                    + "-" + queueName
                    + "|Took " + Utilities.CalculateTAT(tat) + " mSec(s)"
                    + "|Exception:", e);
            processed = false;
        }

        return processed;
    }

    /**
     * handleAbblyResponse
     *
     * @param body
     * @param queueName
     * @return
     */
    @SuppressWarnings("UseSpecificCatch")
    private boolean handleAbblyResponse(byte[] body, String queueName) {
        Instant tat = Instant.now();
        boolean processed = false;
        String message = new String(body, StandardCharsets.UTF_8);
        try {
            logger.info(Utilities.getLogPreString("LiveBetting")
                    + "handleAbblyResponse()"
                    + "|Received"
                    + "-" + queueName
                    + "- Msg:" + message);

            Abbly abbly = new Abbly();
            try {
                JSONObject obj = new JSONObject(message);
                abbly.setBetId(obj.getLong("BetId"));
                abbly.setTimestamp(obj.getLong("timestamp"));
                abbly.setIsBetAllowed(obj.getBoolean("IsBetAllowed"));
                abbly.setMaxAllowedStake(obj.getDouble("maxAllowedStake"));
                abbly.setBetAssessmentResult(obj.getString("BetAssessmentResult"));

                JSONArray legs = obj.optJSONArray("Legs");
                abbly.setBetLegs(new JSONArray());
                if (legs == null) {
                    abbly.setBetLegs(new JSONArray());

                } else {
                    abbly.setBetLegs(obj.getJSONArray("Legs"));
                }
            } catch (Exception js) {
                logger.error(Utilities.getLogPreString("LiveBetting")
                        + "handleAbblyResponse()"
                        + "-" + queueName
                        + "|Took " + Utilities.CalculateTAT(tat) + " mSec(s)"
                        + "|Exception:" + js.getMessage());
                return true;
            }

            boolean kraReport = false;
            Map<String, Object> betInfo = null;
            try (final java.sql.Connection dbConn = db.ReadDataSource("bets").getConnection();) {
                betInfo = RabbitUtils.getSportsBetDetails(abbly.getBetId(), dbConn);
                if (null != betInfo) {
                    kraReport = RabbitUtils.getBetAudits((int) betInfo.get("clientId"),
                            (int) betInfo.get("betType"), "SPORTS", dbConn);
                }
            } catch (Exception e) {
            }

            if (null == betInfo) {
                logger.info(Utilities.getLogPreString("LiveBetting")
                        + "handleAbblyResponse()"
                        + "-" + queueName
                        + "| BetId:" + abbly.getBetId()
                        + "|Bet NOT FOUND!!!");
                return true;
            }

            if (Utilities.FindInIntegerArray(basics.finalBetStatus,
                    (int) betInfo.get("status"))
                    || Utilities.FindInIntegerArray(basics.finalBetRiskStatus,
                            (int) betInfo.get("riskState"))) {
                logger.info(Utilities.getLogPreString("LiveBetting")
                        + "handleAbblyResponse()"
                        + "-" + queueName
                        + "| BetId:" + abbly.getBetId()
                        + "|Bet Already processed!!!");
                return true;
            }

            if ((long) betInfo.get("creditId") > 0) {
                logger.info(Utilities.getLogPreString("LiveBetting")
                        + "handleAbblyResponse()"
                        + "-" + queueName
                        + "| BetId:" + abbly.getBetId()
                        + "|Bet Already Credited or Refunded!!!");
                return true;
            }

            JSONObject extraData = new JSONObject(betInfo.get("eData").toString());
            String date = extraData.getJSONObject("risk").getString("date");

            extraData.put("risk", new JSONObject()
                    .put("isBetAllowed", abbly.isIsBetAllowed())
                    .put("betDesc", abbly.getBetAssessmentResult())
                    .put("maxAllowedStake", abbly.getMaxAllowedStake())
                    .put("completedDate", Utilities.now("yyyy-MM-dd HH:mm:ss"))
                    .put("date", date));

            boolean refund = false;
            if (!abbly.isIsBetAllowed() || !abbly.getBetAssessmentResult()
                    .equalsIgnoreCase("BetAllowed")) {

                logger.info(Utilities.getLogPreString("LiveBetting")
                        + "handleAbblyResponse()"
                        + "-" + queueName
                        + "| BetId:" + abbly.getBetId()
                        + "| Refunding bet!!!"
                        + "| " + abbly.getBetAssessmentResult());

                int betStatus = 9;//Cancelled
                if (!RabbitUtils.updateStatements(db.WriteDataSource("bets"),
                        "UPDATE sports_bet SET status=?,risk_state=?,extra_data=? WHERE bet_id=? LIMIT 1",
                        new Object[]{betStatus, 3, extraData.toString(), abbly.getBetId()})) {
                    return false;
                }

                try (final java.sql.Connection wrDb
                        = db.WriteDataSource("bets").getConnection();
                        final PreparedStatement ps = wrDb.prepareStatement(
                                "UPDATE sports_bet_slip SET resulting_type=?,status=?"
                                + ",outcome_name=?,winning_outcome=? WHERE bet_id=? AND selection_id=?");
                        final PreparedStatement psBets
                        = wrDb.prepareStatement("UPDATE sports_bet SET status=?"
                                + ",risk_state=?,extra_data=? WHERE bet_id=? LIMIT 1");) {
                    wrDb.setAutoCommit(false);

                    int counter = 0;
                    JSONArray jArr = abbly.getBetLegs();
                    if (jArr.length() > 0) {
                        for (int i = 0; i < jArr.length(); i++) {
                            JSONObject leg = jArr.getJSONObject(i);

                            String outcome = leg.getString("SelectionStatus");
                            if (leg.has("RejectReason")) {
                                outcome = outcome + ";" + leg.getJSONObject("RejectReason")
                                        .getString("ReasonCode");
                                outcome = outcome.concat(";" + leg.getJSONObject("RejectReason")
                                        .getString("ReasonMessage"));
                            }

                            logger.info(Utilities.getLogPreString("LiveBetting")
                                    + "handleAbblyResponse()"
                                    + "-" + queueName
                                    + "| BetId:" + abbly.getBetId()
                                    + "| SelectionId:" + leg.getString("SelectionId")
                                    + "| Status:" + betStatus
                                    + "| outcome:" + outcome);

                            //if (!leg.getString("SelectionStatus").equalsIgnoreCase("Open")) {
                            ps.setString(1, "ABBLY_RESULTING");
                            ps.setInt(2, betStatus);
                            ps.setString(3, "Cancelled");
                            ps.setString(4, outcome);
                            ps.setLong(5, abbly.getBetId());
                            ps.setString(6, leg.getString("SelectionId"));
                            ps.addBatch();
                            ++counter;
                            //}
                        }

                        if (counter > 0) {
                            ps.executeBatch();
                        }
                    } else {
                        try (final PreparedStatement psAll = wrDb.prepareStatement(
                                "UPDATE sports_bet_slip SET resulting_type=?,status=?"
                                + ",outcome_name=?,winning_outcome=? WHERE bet_id=?");) {
                            psAll.setString(1, "ABBLY_RESULTING");
                            psAll.setInt(2, betStatus);
                            psAll.setString(3, "Cancelled");
                            psAll.setString(4, abbly.getBetAssessmentResult());
                            psAll.setLong(5, abbly.getBetId());
                            int updateState = psAll.executeUpdate();
                            if (updateState < 1) {
                                wrDb.rollback();
                                throw new SQLException("ABBLY update failed for BetId:" + abbly.getBetId());
                            }
                        } catch (SQLException se) {
                            throw se;
                        }
                    }

                    psBets.setInt(1, betStatus);
                    psBets.setInt(2, 3);
                    psBets.setString(3, extraData.toString());
                    psBets.setLong(4, abbly.getBetId());
                    int updateState = psBets.executeUpdate();
                    if (updateState < 1) {
                        wrDb.rollback();
                        throw new SQLException("ABBLY update failed for BetId:" + abbly.getBetId());
                    }

                    wrDb.commit();

                    refund = true;
                } catch (SQLException se) {
                    throw se;
                }
            }

            String mobileNumber = extraData.getString("msisdn");
            if (refund) {
                logger.info(Utilities.getLogPreString("LiveBetting")
                        + "handleAbblyResponse()"
                        + "-" + queueName
                        + "| BetId:" + abbly.getBetId()
                        + "| mobileNumber:" + mobileNumber
                        + "| Refunding the bet"
                        + "| Reason:" + abbly.getBetAssessmentResult());

                String ipAddress = extraData.getString("ip");
                String signature = Utilities.CreateMd5(ipAddress
                        + "" + abbly.getBetId()
                        + "" + (long) betInfo.get("debitId")
                        + "" + betInfo.get("profileId"));

                Queue.publishMessage(
                        new JSONObject()
                                .put("type", "SPORTS")
                                .put("reason", "(ABBLY) Refund Reason:" + abbly.getBetAssessmentResult())
                                .put("bet_id", abbly.getBetId())
                                .put("bet_reference", betInfo.get("betReference").toString())
                                .put("ip_address", ipAddress)
                                .put("trxn_id", (long) betInfo.get("debitId"))
                                .put("bet_amount", (double) betInfo.get("stake"))
                                .put("customer_id", (long) betInfo.get("profileId"))
                                .put("mobile_number", mobileNumber)
                                .put("signature", signature)
                                .put("timestamp", Utilities.now("yyyy-MM-dd HH:mm:ss"))
                                .toString(),
                        "REFUNDS", "REFUNDS", "REFUNDS",
                        null, null);

                return true;
            }

            String qN = "";
            Object[] sqlStmtParams = new Object[]{1, extraData.toString(), abbly.getBetId()};
            String updateSql = "UPDATE sports_bet SET risk_state=?,extra_data=? WHERE bet_id=? LIMIT 1";

//            if (kraReport) {
//                updateSql = "UPDATE sports_bet SET risk_state=?,extra_data=?"
//                        + ",kra_report=? WHERE bet_id=? LIMIT 1";
//                sqlStmtParams = new Object[]{1, extraData.toString(), 1, abbly.getBetId()};
//            }

            if (!RabbitUtils.updateStatements(db.WriteDataSource("bets"),
                    updateSql, sqlStmtParams)) {
                return false;
            }

            if (kraReport) {
//                qN = "KRA_STAKE";
//                int rand = Utilities.GetRandomInt(0, 1);
//                if (rand > 0) {
//                    qN += "_" + rand;
//                }
//
//                ZonedDateTime issuedAt = ZonedDateTime.now(ZoneOffset.UTC);
//                if (!Queue.publishMessage(new JSONObject()
//                        .put("bet_id", abbly.getBetId())
//                        .put("bet_reference", betInfo.get("betReference"))
//                        .put("bet_type", "SPORTS")
//                        .put("timestamp", issuedAt.toInstant().getEpochSecond())
//                        .put("mobile_number", mobileNumber)
//                        .toString(), qN, qN, qN, null, null)) {
//                    RabbitUtils.updateStatements(db.WriteDataSource("bets"),
//                            "UPDATE sports_bet SET kra_report=? WHERE bet_id=? LIMIT 1",
//                            new Object[]{0, abbly.getBetId()});
//                }
            }

            if ((double) betInfo.get("stake") >= 20) {
                String messageOut = "Bet Confirmed!"
                        + "\n-\n"
                        + "Bet ID: " + betInfo.get("betReference")
                        + "\nStake: " + betInfo.get("currency") + "." + betInfo.get("stake")
                        + "\nPossible Payout: " + betInfo.get("currency") + "." + betInfo.get("payout")
                        + "\nDate: " + Utilities.now("yyyy-MM-dd HH:mm:ss")
                        + "\n-\n"
                        + "Play now: https://mossbets.com"
                        + "\nHelp:" + basics.HelpLine;

                long outboxId = RabbitUtils.createSMSOutBox((long) betInfo.get("profileId"),
                        basics.SenderId, messageOut, "DEPOSIT",
                        db.WriteDataSource("profile"));

                qN = Utilities.getRandomValue(props.SmsOutboxQueue());

                Queue.publishMessage(new JSONObject()
                        .put("campaign_id", outboxId)
                        .put("message_pages", 1)
                        .put("message", messageOut)
                        .put("sms-id", "QUICKSENDVERIFICATION")
                        .put("network_regex", 1)
                        .put("network", "SAFARICOM")
                        .put("alert_type", "TRANSACTIONAL")
                        .put("recipients", mobileNumber)
                        .put("outbox_id", outboxId)
                        .put("short_code", basics.SenderId)
                        .put("gateway", 1)
                        .put("dlr_url", "")
                        .put("auth_token", "auth_token_api")
                        .put("date_created", Utilities.now("yyyy-MM-dd HH:mm:ss"))
                        .toString(), qN, qN, qN,
                        null, null);
            }

            if (extraData.has("issued")) {
                try {
                    String messageOut = "Congratulations!!"
                            + "\nYou've been awarded " + extraData.getJSONObject("issued").getString("name")
                            + "\nAmount " + betInfo.get("currency") + "." + extraData
                            .getJSONObject("issued").getDouble("amnt")
                            + "\nExpires on " + extraData.getJSONObject("issued").getString("expr")
                            + "\nPlay now: https://mossbets.com"
                            + "\nHelpline:" + basics.HelpLine;
                    long outboxId = RabbitUtils.createSMSOutBox((long) betInfo.get("profileId"),
                            basics.SenderId, messageOut, "BONUS",
                            db.WriteDataSource("profile"));

                    qN = Utilities.getRandomValue(props.SmsOutboxQueue());

                    Queue.publishMessage(new JSONObject()
                            .put("campaign_id", outboxId)
                            .put("message_pages", 1)
                            .put("message", messageOut)
                            .put("sms-id", "QUICKSENDVERIFICATION")
                            .put("network_regex", 1)
                            .put("network", "SAFARICOM")
                            .put("alert_type", "TRANSACTIONAL")
                            .put("recipients", mobileNumber)
                            .put("outbox_id", outboxId)
                            .put("short_code", basics.SenderId)
                            .put("gateway", 1)
                            .put("dlr_url", "")
                            .put("auth_token", "auth_token_api")
                            .put("date_created", Utilities.now("yyyy-MM-dd HH:mm:ss"))
                            .toString(), qN, qN, qN,
                            null, null);
                } catch (JSONException jse) {
                }
            }

            try {
                String authToken = GetBFRAuthToken();
                if (!Utilities.isBlank(authToken)) {
                    Map<String, String> httpHeader = new HashMap<>();
                    httpHeader.put("User-Agent", basics.UA);
                    httpHeader.put("X-Organization-Id", "Mossbets/v1");
                    httpHeader.put("Authorization", "Bearer " + authToken);
                    httpHeader.put("x-api-key", props.geniusXApiKey());

                    JSONArray legs = new JSONArray();
                    try (final java.sql.Connection betDb = db.ReadDataSource("bets").getConnection();
                            PreparedStatement ps = betDb.prepareStatement(
                                    "SELECT IF(live_bet=0,'PreMatch','InPlay') AS game_state"
                                    + ",odd_value as odds FROM sports_bet_slip "
                                    + "WHERE selection_id=? AND bet_id=?");) {
                        JSONArray jArr = abbly.getBetLegs();
                        for (int i = 0; i < jArr.length(); i++) {
                            JSONObject leg = jArr.getJSONObject(i);

                            ps.setString(1, leg.getString("SelectionId"));
                            ps.setLong(2, abbly.getBetId());
                            try (ResultSet rs = ps.executeQuery()) {
                                while (rs.next()) {
                                    legs.put(new JSONObject()
                                            .put("price", rs.getString("odds"))
                                            .put("gameState", rs.getString("game_state"))
                                            .put("status", leg.getString("SelectionStatus"))
                                            .put("betgeniusContent", new JSONObject()
                                                    .put("selectionId", leg.getString("SelectionId"))));
                                }
                            } catch (SQLException s) {
                            }
                        }
                    } catch (SQLException s) {
                    }

                    if (legs.length() > 0) {
                        Instant httpTime = Instant.now();
                        Map<String, Object> httpResponse
                                = ApiCalls.sendHttpJsonPostData(props.geniusRiskBfrUrl(),
                                        new JSONObject()
                                                .put("id", String.valueOf(abbly.getBetId()))
                                                .put("betPlacedTimestampUTC", httpTime)
                                                .put("betUpdatedTimestampUTC", httpTime)
                                                .put("bookmakerName", "mossbets")
                                                .put("playerId", betInfo.get("profileId").toString())
                                                .put("totalStake", ((double) betInfo.get("stake")
                                                        - (double) betInfo.get("exciseTax")))
                                                .put("currencyCode", betInfo.get("currency"))
                                                .put("priority", 1)
                                                .put("status", "Open")
                                                .put("legs", legs).toString(),
                                        httpHeader,
                                        basics,
                                        logger, false);
                        logger.info(Utilities.getLogPreString("ApiCalls")
                                + "handleAbblyResponse()"
                                + "|Took " + Utilities.CalculateTAT(httpTime) + " mSec(s)"
                                + "|URL:" + props.geniusRiskBfrUrl()
                                + "|BetId:" + abbly.getBetId()
                                + "|mobileNumber:" + mobileNumber
                                + "|BFR Results =>" + httpResponse.toString());

                        if ((int) httpResponse.get("statusCode") == 200) {
                            extraData.put("genius_bfr", new JSONObject()
                                    .put("betTime", httpTime)
                                    .put("posted", true));
                            RabbitUtils.updateStatements(db.WriteDataSource("bets"),
                                    "UPDATE sports_bet SET extra_data=? WHERE bet_id=? LIMIT 1",
                                    new Object[]{extraData.toString(), abbly.getBetId()});
                        }
                    }
                }
            } catch (Exception e) {
                logger.error(Utilities.getLogPreString("LiveBetting")
                        + "handleAbblyResponse()"
                        + "|BetId:" + abbly.getBetId()
                        + "|mobileNumber:" + mobileNumber
                        + "|Exception:", e);
            }

            processed = true;
        } catch (Exception e) {
            logger.error(Utilities.getLogPreString("LiveBetting")
                    + "handleAbblyResponse()"
                    + "-" + queueName
                    + "|Took " + Utilities.CalculateTAT(tat) + " mSec(s)"
                    + "|Exception:", e);
            processed = false;
        }

        return processed;
    }

    /**
     * cleanup
     */
    @PreDestroy
    @SuppressWarnings("UseSpecificCatch")
    void cleanup() {
        logger.info(Utilities.getLogPreString("LiveBetting")
                + "cleanup()"
                + "|Shutting DOWN RabbitMQ Consumer gracefully...");
        running = false;
        try {
            if (connection != null && connection.isOpen()) {
                connection.close();
            }

            logger.info(Utilities.getLogPreString("LiveBetting")
                    + "cleanup()"
                    + "|RabbitMQ Connection closed");
        } catch (Exception e) {
            logger.fatal(Utilities.getLogPreString("LiveBetting")
                    + "cleanup()"
                    + "|Exception", e);
        }
    }
}

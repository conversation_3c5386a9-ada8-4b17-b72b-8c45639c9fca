package com.mossbets.app.rabbitmq.consumers;

import com.mossbets.app.rabbitmq.models.MpesaResult;
import com.mossbets.app.rabbitmq.models.MpesaWithdrawal;
import com.mossbets.app.rabbitmq.utils.HashUtils;
import com.mossbets.app.rabbitmq.utils.RabbitUtils;
import com.mossbets.app.resources.DB;
import com.mossbets.app.utils.ApiCalls;
import com.mossbets.app.utils.JedisUtils;
import com.mossbets.app.utils.Queue;
import com.mossbets.app.utils.Utilities;
import com.mossbets.app.utils.props.Basics;
import com.mossbets.app.utils.props.Props;
import com.rabbitmq.client.AMQP;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;
import com.rabbitmq.client.DefaultConsumer;
import com.rabbitmq.client.Envelope;
import io.quarkus.runtime.ShutdownEvent;
import io.quarkus.runtime.StartupEvent;
import io.quarkus.scheduler.Scheduled;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.event.Observes;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.SQLIntegrityConstraintViolationException;
import java.time.Instant;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import org.codehaus.jettison.json.JSONException;
import org.codehaus.jettison.json.JSONObject;
import org.jboss.logging.Logger;

@ApplicationScoped
public class WithdrawalApp {

    @Inject
    Props props;

    @Inject
    Basics basics = new Basics();

    @Inject
    DB db;

    @Inject
    Logger logger;

    @Inject
    private Queue Queue;

    @Inject
    @Named("stkExecutor")
    ExecutorService es;

    @Inject
    ScheduledExecutorService scheduler;

    private Connection connection;
    private int reconnectAttempt = 0;
    private final int maxBackoffSeconds = 60;
    private volatile boolean running = true;

    void onStart(@Observes StartupEvent ev) {
        logger.info(Utilities.getLogPreString("WithdrawalApp")
                + "onStart()"
                + "|Starting RabbitMQ Consumer...");
        scheduleReconnect(0);
    }

    void onStop(@Observes ShutdownEvent event) {
        logger.info(Utilities.getLogPreString("WithdrawalApp")
                + "onStop()"
                + "|Application shutting down...");
        cleanup();
    }

    @PostConstruct
    void startOnAppBoot() {
        scheduleReconnect(0); // ✅ triggers startup
    }

    /**
     * scheduleReconnect
     *
     * @param delaySeconds
     */
    private void scheduleReconnect(int delaySeconds) {
        scheduler.schedule(this::connectWithBackoff, delaySeconds, TimeUnit.SECONDS);
    }

    /**
     * connectWithBackoff
     */
    private void connectWithBackoff() {
        if (!running) {
            return;
        }

        try {
            connectAndStartConsuming();
            logger.info(Utilities.getLogPreString("WithdrawalApp")
                    + "connectWithBackoff()"
                    + "|RabbitMQ Consumer connected and consuming....");
            reconnectAttempt = 0; // reset on success
        } catch (Exception e) {
            reconnectAttempt++;
            int backoff = calculateBackoff(reconnectAttempt);
            logger.errorf(Utilities.getLogPreString("WithdrawalApp")
                    + "connectWithBackoff()"
                    + "|Reconnect failed (attempt #%d). Retrying in %ds...",
                    reconnectAttempt, backoff);
            scheduleReconnect(backoff);
        }
    }

    /**
     * calculateBackoff
     *
     * @param attempt
     * @return
     */
    private int calculateBackoff(int attempt) {
        int base = (int) Math.min(Math.pow(2, attempt), maxBackoffSeconds);
        int jitter = ThreadLocalRandom.current().nextInt(0, 5);
        return base + jitter; // Exponential + jitter
    }

    /**
     * createFactory
     *
     * @return
     */
    private ConnectionFactory createFactory() {
        ConnectionFactory factory = new ConnectionFactory();
        factory.setHost(props.rabbitMqHost());
        factory.setPort(props.rabbitMqPort());
        factory.setVirtualHost(props.rabbitMqVhost());
        factory.setUsername(props.rabbitMqUsername());
        factory.setPassword(props.rabbitMqPassword());
        factory.setTopologyRecoveryEnabled(props.rabbitMqConRecovery());
        factory.setAutomaticRecoveryEnabled(props.rabbitMqTopologyRecovery());
        factory.setNetworkRecoveryInterval(props.rabbitMqNetworkRecovery());
        return factory;
    }

    /**
     * connectAndStartConsuming
     *
     * @throws Exception
     */
    private void connectAndStartConsuming() throws Exception {
        connection = createFactory().newConnection();
        connection.addShutdownListener(cause -> {
            logger.info(Utilities.getLogPreString("WithdrawalApp")
                    + "connectAndStartConsuming()"
                    + "|Connection shutdown: " + cause.getMessage());
            if (running) {
                scheduleReconnect(0); // retry again
            }
        });

        for (String queue : props.WithdrawalQueues()) {
            String queueName = props.rabbitMqPrefix().concat("_" + queue + "_QUEUE");
            String exchange = props.rabbitMqPrefix().concat("_" + queue + "_EXCHANGE");
            String route = props.rabbitMqPrefix().concat("_" + queue + "_ROUTE");

            Channel channel = connection.createChannel();
            channel.queueDeclare(queueName, true, false, false, null);
            channel.exchangeDeclare(exchange, "direct", true, false, null);
            channel.queueBind(queueName, exchange, route);
            channel.basicQos(props.rabbitMqPrefetchCount());
            channel.basicConsume(queueName, false, new DefaultConsumer(channel) {
                @Override
                @SuppressWarnings("UseSpecificCatch")
                public void handleDelivery(String consumerTag, Envelope envelope,
                        AMQP.BasicProperties properties, byte[] body) {
                    long deliveryTag = envelope.getDeliveryTag();
                    try {
                        es.submit(() -> handleMessage(body, queueName, channel, deliveryTag));
                    } catch (Exception ex) {
                        logger.error(Utilities.getLogPreString("WithdrawalApp")
                                + "connectAndStartConsuming()"
                                + "|Queue " + queueName
                                + "|ConsumerTag:" + consumerTag
                                + "|Exception: " + ex.getMessage());
                        rejectAck(channel, deliveryTag, true);
                    }
                }
            });

            logger.infof(Utilities.getLogPreString("WithdrawalApp")
                    + "connectAndStartConsuming()"
                    + "|Queue [%s]"
                    + "|Exchange [%s]"
                    + "|Route [%s]", queueName, exchange, route);
        }
    }

    /**
     * cleanup
     */
    @PreDestroy
    @SuppressWarnings("UseSpecificCatch")
    void cleanup() {
        logger.info(Utilities.getLogPreString("WithdrawalApp")
                + "cleanup()"
                + "|Shutting DOWN RabbitMQ Consumer gracefully...");
        running = false;
        try {
            if (connection != null && connection.isOpen()) {
                connection.close();
            }

            logger.info(Utilities.getLogPreString("WithdrawalApp")
                    + "cleanup()"
                    + "|RabbitMQ Connection closed");
        } catch (Exception e) {
            logger.fatal(Utilities.getLogPreString("WithdrawalApp")
                    + "cleanup()"
                    + "|Exception", e);
        }
    }

    /**
     * isHealthy
     *
     * @return
     */
    public boolean isHealthy() {
        return connection != null && connection.isOpen();
    }

    /**
     * reconnectWithDelay
     */
    private void reconnectWithDelay() {
        reconnectAttempt++;
        int delay = calculateBackoff(reconnectAttempt);

        logger.warnf(Utilities.getLogPreString("WithdrawalApp")
                + "reconnectWithDelay()"
                + "|Scheduling Reconnection in %d seconds (attempt #%d)...",
                delay, reconnectAttempt);

        scheduler.schedule(this::connectWithBackoff, delay, TimeUnit.SECONDS);
    }

    @Scheduled(every = "10m", concurrentExecution = Scheduled.ConcurrentExecution.SKIP)
    public void checkIfAppIsRunning() {
        if (isHealthy()) {
            logger.info(Utilities.getLogPreString("WithdrawalApp")
                    + "AppChecks() => ALIVE");
        } else if (running) {
            logger.info(Utilities.getLogPreString("WithdrawalApp")
                    + "AppChecks()"
                    + "|RabbitMQ Connection is DOWN. Attempting to reconnect...");
            reconnectWithDelay();
        }
    }

    /**
     * acknowledge
     *
     * @param channel
     * @param deliveryTag
     * @throws IOException
     */
    public void successAck(Channel channel, long deliveryTag) throws IOException {
        try {
            if (channel != null && channel.isOpen()) {
                channel.basicAck(deliveryTag, false);
                logger.infof(Utilities.getLogPreString("WithdrawalApp")
                        + "successAck()"
                        + "|ACK [%s] tag:%d", "Success", deliveryTag);
            }
        } catch (IOException e) {
            throw e;
        }
    }

    /**
     *
     * @param channel
     * @param deliveryTag
     * @param requeue
     */
    public void rejectAck(Channel channel, long deliveryTag, boolean requeue) {
        try {
            if (channel != null && channel.isOpen()) {
                channel.basicReject(deliveryTag, requeue);
                logger.infof(Utilities.getLogPreString("WithdrawalApp")
                        + "rejectAck()"
                        + "|ACK [%s] tag:%d", "Reject", deliveryTag);
            }
        } catch (IOException e) {
            logger.error(Utilities.getLogPreString("WithdrawalApp")
                    + "rejectAck()"
                    + "IOException:" + e.getMessage()
                    + "|ACK Failed tag:" + deliveryTag);
        }
    }

    /**
     * handleMessage
     *
     * @param body
     * @param queueName
     * @param channel
     * @param deliveryTag
     */
    @SuppressWarnings("UseSpecificCatch")
    private void handleMessage(byte[] body, String queueName, Channel channel, long deliveryTag) {
        Instant tat = Instant.now();
        try {
            if (queueName.contains("B2C_RESULT")) {
                if (handleB2Callback(body, queueName)) {
                    successAck(channel, deliveryTag);
                } else {
                    rejectAck(channel, deliveryTag, true);
                }

                logger.info(Utilities.getLogPreString("WithdrawalApp")
                        + "handleMessage()"
                        + "-" + queueName
                        + "|Took " + Utilities.CalculateTAT(tat) + " mSec(s)"
                        + "|Tag:" + deliveryTag);
                return;
            }

            if (handleWithdrawal(body, queueName)) {
                successAck(channel, deliveryTag);
            } else {
                rejectAck(channel, deliveryTag, true);
            }

            logger.info(Utilities.getLogPreString("WithdrawalApp")
                    + "handleMessage()"
                    + "-" + queueName
                    + "|Took " + Utilities.CalculateTAT(tat) + " mSec(s)"
                    + "|Tag:" + deliveryTag);
        } catch (Exception e) {
            rejectAck(channel, deliveryTag, true);

            logger.error(Utilities.getLogPreString("WithdrawalApp")
                    + "handleMessage()"
                    + "-" + queueName
                    + "|Took " + Utilities.CalculateTAT(tat) + " mSec(s)"
                    + "|Exception:" + e.getMessage());
        }
    }

    /**
     * handleCallback
     *
     * @param body
     * @param queueName
     * @return
     */
    private boolean handleB2Callback(byte[] body, String queueName) {
        boolean processed = false;
        String message = new String(body, StandardCharsets.UTF_8);
        try {
            logger.info(Utilities.getLogPreString("WithdrawalApp")
                    + "handleB2Callback()"
                    + "|Received"
                    + "-" + queueName
                    + "- Msg:" + message);

            MpesaResult B2cResult = new MpesaResult();
            try {
                JSONObject obj = new JSONObject(message);
                B2cResult.setResultType(obj.getInt("ResultType"));
                B2cResult.setResultCode(obj.getInt("ResultCode"));
                B2cResult.setResultDesc(obj.getString("ResultDesc"));
                B2cResult.setSignature(obj.getString("signature"));
                B2cResult.setTransactionId(obj.getString("TransactionId"));
                B2cResult.setConversationId(obj.getString("ConversationId"));
                B2cResult.setOriginalConversationId(obj.getString("OriginalConversationId"));
                B2cResult.setUniqueId(obj.getString("uniqueId"));
                B2cResult.setOrgShortCode(obj.getString("orgShortCode"));
                B2cResult.setOrgName(obj.getString("orgName"));
                B2cResult.setIpAddress(obj.getString("ipAddress"));

                B2cResult.setReceiverPartyPublicName("");
                B2cResult.setAmount(0.0);
                B2cResult.setCharges(0.0);
                B2cResult.setChargesPaid(0.0);
                B2cResult.setOrgUtilityBalance(0.0);
                B2cResult.setOrgWorkingBalance(0.0);

                if ((B2cResult.getResultCode() == 0) && (B2cResult.getResultType() == 0)) {
                    if (obj.has("ReceiverPartyPublicName")) {
                        B2cResult.setReceiverPartyPublicName(
                                obj.getString("ReceiverPartyPublicName"));
                    }

                    B2cResult.setAmount(obj.getDouble("amount"));
                    B2cResult.setCharges(obj.getDouble("charges"));
                    B2cResult.setChargesPaid(obj.getDouble("chargesPaid"));
                    B2cResult.setInitiatedDate(obj.getString("initiatedDate"));
                    B2cResult.setOrgUtilityBalance(obj.getDouble("orgUtilityBalance"));
                    B2cResult.setOrgWorkingBalance(obj.getDouble("orgWorkingBalance"));
                    B2cResult.setTransactionCompletedDateTime(obj
                            .getString("TransactionCompletedDateTime"));
                }
            } catch (JSONException js) {
                logger.error(Utilities.getLogPreString("WithdrawalApp")
                        + "handleB2Callback()"
                        + "-" + queueName
                        + "|Exception:" + js.getMessage());
                return true;
            }

            String[] requestArr = B2cResult.getUniqueId().split("-");
            if (!requestArr[0].equalsIgnoreCase("MB") || requestArr.length != 3) {
                logger.info(Utilities.getLogPreString("WithdrawalApp")
                        + "handleB2Callback()"
                        + "|RequestId:" + B2cResult.getUniqueId()
                        + "|MpesaId:" + B2cResult.getTransactionId()
                        + "|Invalid B2C Result!!!");
                return true;
            }

            long requestId = Long.parseLong(requestArr[2]);

            Map<String, Object> custInfo = null;
            Map<String, Object> wdrInfo = null;
            Map<String, Object> withdrawInfo = null;
            try (final java.sql.Connection dbConn = db.ReadDataSource("trxn").getConnection();) {
                withdrawInfo = RabbitUtils.getWithdrawalRequest(requestId, dbConn);
                if (null != withdrawInfo) {
                    custInfo = RabbitUtils.getCustomerDetails((long) withdrawInfo.get("profileId"), dbConn);
                    wdrInfo = RabbitUtils.getWithdrawalDlrRequest(requestId, dbConn);
                }
            } catch (Exception e) {
            }

            if ((null == withdrawInfo) || (null == custInfo)) {
                logger.info(Utilities.getLogPreString("WithdrawalApp")
                        + "handleB2Callback()"
                        + "|RequestId:" + B2cResult.getUniqueId()
                        + "|MpesaId:" + B2cResult.getTransactionId()
                        + "|Request NOT FOUND***Retry!!!");
                return false;
            }

            logger.info(Utilities.getLogPreString("WithdrawalApp")
                    + "handleB2Callback()"
                    + "|RequestId:" + B2cResult.getUniqueId()
                    + "|MpesaId:" + B2cResult.getTransactionId()
                    + "<<--CustInfo-->>" + custInfo.toString());

            long profileId = (long) withdrawInfo.get("profileId");
            try (final java.sql.Connection tDb = db.WriteDataSource("trxn").getConnection();
                    final java.sql.Connection pDb = db.WriteDataSource("profile").getConnection();) {
                tDb.setAutoCommit(false);
                pDb.setAutoCommit(false);

                long trxnId = 0;
                if (null == wdrInfo) {
                    try (final PreparedStatement ps = tDb.prepareStatement(
                            "INSERT withdrawal_dlr(request_id,response_code,response_status"
                            + ",response_description,receipt_number,receiver_name"
                            + ",transaction_amount,org_working_balance,org_utility_balance"
                            + ",extra_data,ip_address,processed,transaction_completed_date"
                            + ",created_at) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,NOW,NOW())",
                            PreparedStatement.RETURN_GENERATED_KEYS);) {
                        ps.setLong(1, requestId);
                        ps.setInt(2, B2cResult.getResultCode());
                        ps.setInt(3, B2cResult.getResultType());
                        ps.setString(4, B2cResult.getResultDesc());
                        ps.setString(5, B2cResult.getTransactionId());
                        ps.setString(6, B2cResult.getReceiverPartyPublicName());
                        ps.setDouble(7, B2cResult.getAmount());
                        ps.setDouble(8, B2cResult.getOrgWorkingBalance());
                        ps.setDouble(9, B2cResult.getOrgUtilityBalance());
                        ps.setString(10, new JSONObject()
                                .put("msisdn", custInfo.get("msisdn"))
                                .put("paybill", B2cResult.getOrgShortCode())
                                .put("resultIpAddress", B2cResult.getIpAddress())
                                .toString());
                        ps.setString(11, B2cResult.getIpAddress());
                        ps.setInt(12, 2);
                        trxnId = ps.executeUpdate();
                        try (ResultSet rs = ps.getGeneratedKeys()) {
                            if (rs.next()) {
                                trxnId = rs.getLong(1);
                            }
                        }

                        if (trxnId < 1) {
                            tDb.rollback();
                            throw new SQLException("Withdrawal Dlr creation failed "
                                    + "|RequestId:" + requestId);
                        }
                    } catch (SQLIntegrityConstraintViolationException e) {
                        if (e.getErrorCode() == 1062 || e.getSQLState().startsWith("23")) {
                            logger.error(Utilities.getLogPreString("WithdrawalApp")
                                    + "handleB2Callback()"
                                    + "|RequestId:" + B2cResult.getUniqueId()
                                    + "|MpesaId:" + B2cResult.getTransactionId()
                                    + "|Duplicate‑entry / unique constraint violations"
                                    + "|Exception:" + e.getMessage());
                            return true;
                        }
                        throw e;
                    } catch (SQLException se) {
                        throw se;
                    }
                } else {
                    if (!Utilities.FindInIntegerArray(new int[]{0, 5},
                            (int) wdrInfo.get("processed"))) {
                        logger.info(Utilities.getLogPreString("WithdrawalApp")
                                + "handleB2Callback()"
                                + "|RequestId:" + B2cResult.getUniqueId()
                                + "|MpesaId:" + B2cResult.getTransactionId()
                                + "|Transactions is already Processed!!!");
                        return true;
                    }

                    try (final PreparedStatement psUpdate = tDb.prepareStatement(
                            "UPDATE withdrawal_dlr SET response_code=?,response_status=?"
                            + ",response_description=?,receiver_name=?,transaction_amount=?"
                            + ",org_working_balance=?,org_utility_balance=?,extra_data=?"
                            + ",processed=?,receipt_number=? WHERE id=? LIMIT 1")) {
                        JSONObject extraData = new JSONObject(
                                wdrInfo.get("extraData").toString());

                        psUpdate.setInt(1, B2cResult.getResultCode());
                        psUpdate.setInt(2, B2cResult.getResultType());
                        psUpdate.setString(3, B2cResult.getResultDesc());
                        psUpdate.setString(4, B2cResult.getReceiverPartyPublicName());
                        psUpdate.setDouble(5, B2cResult.getAmount());
                        psUpdate.setDouble(6, B2cResult.getOrgWorkingBalance());
                        psUpdate.setDouble(7, B2cResult.getOrgUtilityBalance());
                        psUpdate.setString(8, extraData
                                .put("requestDesc", wdrInfo.get("responseDescription"))
                                .put("receiptNumber", wdrInfo.get("receiptNumber"))
                                .put("resultIpAddress", B2cResult.getIpAddress())
                                .toString());
                        psUpdate.setInt(9, 2);
                        psUpdate.setString(10, B2cResult.getTransactionId());
                        psUpdate.setLong(11, (long) wdrInfo.get("withdrawId"));
                        int updateState = psUpdate.executeUpdate();
                        if (updateState < 1) {
                            throw new SQLException("Withdrawal Dlr creation failed "
                                    + "|RequestId:" + requestId);
                        }

                        trxnId = extraData.getLong("trxnId");
                    } catch (Exception se) {
                        tDb.rollback();
                        throw se;
                    }
                }

                if (null == custInfo.get("name")) {
                    if (!Utilities.isBlank(B2cResult.getReceiverPartyPublicName())) {
                        String[] receiverArr = Arrays
                                .stream(B2cResult.getReceiverPartyPublicName()
                                        .split("-"))
                                .filter(token -> !token.isEmpty())
                                .toArray(String[]::new);
                        if (receiverArr.length > 0) {
                            try (final PreparedStatement psUpdate = pDb.prepareStatement(
                                    "UPDATE profile SET name=? WHERE id=? LIMIT 1")) {
                                psUpdate.setString(1, receiverArr[1].trim());
                                psUpdate.setLong(2, profileId);

                                int updateState = psUpdate.executeUpdate();
                                if (updateState < 1) {
                                    throw new SQLException("Update Profile Name failed "
                                            + "|RequestId:" + requestId);
                                }
                            } catch (Exception se) {
                                pDb.rollback();
                                throw se;
                            }
                        }
                    }
                }

                if ((B2cResult.getResultCode() != 0) && (B2cResult.getResultType() == 0)) {
                    logger.info(Utilities.getLogPreString("WithdrawalApp")
                            + "handleB2Callback()"
                            + "|RequestId:" + B2cResult.getUniqueId()
                            + "|MpesaId:" + B2cResult.getTransactionId()
                            + "|ResultCode:" + B2cResult.getResultCode()
                            + "|Currency:" + withdrawInfo.get("currency")
                            + "|Amount:" + withdrawInfo.get("amount")
                            + "|charges:" + withdrawInfo.get("charges")
                            + "|Initiating Wallet Reversal");

                    double reversalAmount = (double) withdrawInfo.get("amount")
                            + (double) withdrawInfo.get("charges") + (double) withdrawInfo.get("withholdingTax");
                    try (final PreparedStatement psUpdate = pDb.prepareStatement(
                            "UPDATE profile_balance SET balance=balance+? "
                            + "WHERE profile_id=? LIMIT 1");
                            final PreparedStatement paUpdate = pDb.prepareStatement(
                                    "UPDATE profile_attribution "
                                    + "SET total_failed_withdrawals=total_failed_withdrawals+? "
                                    + "WHERE profile_id=? LIMIT 1");) {
                        psUpdate.setDouble(1, reversalAmount);
                        psUpdate.setLong(2, profileId);
                        int updateState = psUpdate.executeUpdate();
                        if (updateState < 1) {
                            throw new SQLException("Update Profile Wallet failed "
                                    + "|RequestId:" + requestId);
                        }

                        paUpdate.setDouble(1, reversalAmount);
                        paUpdate.setLong(2, profileId);
                        int updatexState = paUpdate.executeUpdate();
                        if (updatexState < 1) {
                            throw new SQLException("Update Profile Attribution failed "
                                    + "|RequestId:" + requestId);
                        }

                        JSONObject extraData = new JSONObject()
                                .put("trxnId", trxnId)
                                .put("mpesaId", B2cResult.getTransactionId())
                                .put("msisdn", custInfo.get("msisdn"))
                                .put("current_balance", custInfo.get("balance"))
                                .put("current_bonus", custInfo.get("bonus"))
                                .put("paybill", B2cResult.getOrgShortCode());

                        String insertTrxnSql = "INSERT INTO transaction(profile_id"
                                + ",reference_type_id,transaction_type_id,reference_id"
                                + ",amount,currency,source,description,extra_data"
                                + ",created_at) VALUES(?,?,?,?,?,?,?,?,?,NOW())";
                        try (final PreparedStatement psT = tDb.prepareStatement(
                                insertTrxnSql, PreparedStatement.RETURN_GENERATED_KEYS);
                                final PreparedStatement psR = tDb.prepareStatement(
                                        insertTrxnSql,
                                        PreparedStatement.RETURN_GENERATED_KEYS);) {
                            psT.setLong(1, profileId);
                            psT.setInt(2, 3);//WITHDRAWAL_REVERSAL_MPESA
                            psT.setInt(3, basics.TRANSACTION_TYPE_REVERSAL_ID);
                            psT.setLong(4, trxnId);
                            psT.setDouble(5, (double) withdrawInfo.get("amount"));
                            psT.setString(6, withdrawInfo.get("currency").toString());
                            psT.setString(7, "MPESA_WITHDRAWAL_REVERSAL");
                            psT.setString(8, "Reversal on Failed withdrawal. RequestId:" + requestId);
                            psT.setString(9, extraData.toString());
                            long trId = psT.executeUpdate();
                            try (ResultSet rs = psT.getGeneratedKeys()) {
                                if (rs.next()) {
                                    trId = rs.getLong(1);
                                }
                            }

                            if (trId < 1) {
                                throw new SQLException("System TrxnId creation failed "
                                        + "|RequestId:" + requestId
                                        + "|ProfileId:" + profileId);
                            }

                            psR.setLong(1, profileId);
                            psR.setInt(2, 29);//WITHDRAWAL_REVERSAL_MPESA
                            psR.setInt(3, basics.TRANSACTION_TYPE_REVERSAL_ID);
                            psR.setLong(4, trId);
                            psR.setDouble(5, (double) withdrawInfo.get("charges"));
                            psR.setString(6, withdrawInfo.get("currency").toString());
                            psR.setString(7, "MPESA_WITHDRAWAL_CHARGES_REVERSAL");
                            psR.setString(8, "Charges Reversal on Failed withdrawal. "
                                    + "RequestId:" + requestId);
                            psR.setString(9, extraData.toString());
                            long trxnChargesId = psR.executeUpdate();
                            try (ResultSet rs = psR.getGeneratedKeys()) {
                                if (rs.next()) {
                                    trxnChargesId = rs.getLong(1);
                                }
                            }

                            if (trxnChargesId < 1) {
                                throw new SQLException("System TrxnId creation failed "
                                        + "|RequestId:" + requestId
                                        + "|ProfileId:" + profileId);
                            }
                        } catch (SQLException e) {
                            throw e;
                        }
                    } catch (Exception se) {
                        pDb.rollback();
                        tDb.rollback();
                        throw se;
                    }
                } else {
                    /**
                     * The Withholding Tax Implementation
                     */
                    long summaryId = 0;
                    try (PreparedStatement ps = tDb.prepareStatement(
                            "SELECT id FROM compliance_transaction_summary "
                            + "WHERE summary_date=DATE(NOW())");) {
                        try (ResultSet rs = ps.executeQuery()) {
                            if (rs.next()) {
                                summaryId = rs.getLong("id");
                            }
                        } catch (SQLException e) {
                            throw e;
                        }
                    } catch (Exception e) {
                        tDb.rollback();
                        tDb.rollback();
                        throw e;
                    }

                    if (summaryId < 1) {
                        try (PreparedStatement ps = tDb.prepareStatement(
                                "INSERT INTO compliance_transaction_summary"
                                + "(bets,withdrawal_count,payout,witholding_tax,summary_date"
                                + ",created_at) VALUES(?,?,?,?,DATE(NOW()),NOW())",
                                PreparedStatement.RETURN_GENERATED_KEYS);) {
                            ps.setInt(1, 0);
                            ps.setInt(2, 1);
                            ps.setDouble(3, (double) withdrawInfo.get("amount"));
                            ps.setDouble(4, (double) withdrawInfo.get("withholdingTax"));
                            summaryId = ps.executeUpdate();
                            try (ResultSet rs = ps.getGeneratedKeys()) {
                                if (rs.next()) {
                                    summaryId = rs.getLong(1);
                                }
                            }

                            if (summaryId < 1) {
                                throw new SQLException("Failed to create WithholdingTax"
                                        + "|handleB2Callback()"
                                        + "|RequestId:" + B2cResult.getUniqueId()
                                        + "|MpesaId:" + B2cResult.getTransactionId()
                                        + "|ResultCode:" + B2cResult.getResultCode());
                            }
                        } catch (Exception e) {
                            tDb.rollback();
                            tDb.rollback();
                            throw e;
                        }
                    } else {
                        try (PreparedStatement ps = tDb.prepareStatement(
                                "UPDATE compliance_transaction_summary SET "
                                + "withdrawal_count=withdrawal_count+1,payout=payout+?"
                                + ",witholding_tax=witholding_tax+? WHERE id=? LIMIT 1");) {
                            ps.setDouble(1, (double) withdrawInfo.get("amount"));
                            ps.setDouble(2, (double) withdrawInfo.get("withholdingTax"));
                            ps.setLong(3, summaryId);
                            int updateState = ps.executeUpdate();
                            if (updateState < 1) {
                                throw new SQLException("Failed to create WithholdingTax"
                                        + "|handleB2Callback()"
                                        + "|RequestId:" + B2cResult.getUniqueId()
                                        + "|MpesaId:" + B2cResult.getTransactionId()
                                        + "|ResultCode:" + B2cResult.getResultCode());
                            }
                        } catch (Exception e) {
                            tDb.rollback();
                            tDb.rollback();
                            throw e;
                        }
                    }
                }

                pDb.commit();
                tDb.commit();

                logger.info(Utilities.getLogPreString("WithdrawalApp")
                        + "handleB2Callback()"
                        + "|RequestId:" + B2cResult.getUniqueId()
                        + "|MpesaId:" + B2cResult.getTransactionId()
                        + "|ResultCode:" + B2cResult.getResultCode()
                        + "|Completed");
                return true;
            } catch (SQLIntegrityConstraintViolationException e) {
                if (e.getErrorCode() == 1062 || e.getSQLState().startsWith("23")) {
                    logger.error(Utilities.getLogPreString("WithdrawalApp")
                            + "handleB2Callback()"
                            + "|RequestId:" + B2cResult.getUniqueId()
                            + "|MpesaId:" + B2cResult.getTransactionId()
                            + "|Duplicate‑entry / unique constraint violations"
                            + "|Exception:" + e.getMessage());
                    return true;
                }
                throw e;
            } catch (SQLException se) {
                throw se;
            }
        } catch (Exception e) {
            logger.error(Utilities.getLogPreString("WithdrawalApp")
                    + "handleB2Callback()"
                    + "-" + queueName
                    + "|Exception:", e);
            processed = false;
        }

        return processed;
    }

    /**
     * handleWithdrawal
     *
     * @param body
     * @param queueName
     * @return
     */
    @SuppressWarnings("UseSpecificCatch")
    private boolean handleWithdrawal(byte[] body, String queueName) {

        boolean processed = false;
        String message = new String(body, StandardCharsets.UTF_8);
        try {
            logger.info(Utilities.getLogPreString("WithdrawalApp")
                    + "handleWithdrawal()"
                    + "|Received"
                    + "-" + queueName
                    + "- Msg:" + message);

            MpesaWithdrawal withdraw = new MpesaWithdrawal();
            try {
                JSONObject obj = new JSONObject(message);
                withdraw.setTrxnId(obj.getLong("requestId"));
                withdraw.setChannel(obj.getString("channel"));
                withdraw.setIpAddress(obj.getString("ipAddress"));
                withdraw.setSignature(obj.getString("signature"));
                withdraw.setPaybillId(obj.getLong("paybillId"));
                withdraw.setPaybillNumber(obj.getString("paybillNumber"));
                withdraw.setDate(obj.getString("date"));
                withdraw.setIp(obj.getString("ip"));
            } catch (Exception js) {
                logger.error(Utilities.getLogPreString("WithdrawalApp")
                        + "handleWithdrawal()"
                        + "-" + queueName
                        + "|Exception:" + js.getMessage());
                return true;
            }

            Map<String, Object> custInfo = null;
            Map<String, Object> withdrawInfo = null;
            try (final java.sql.Connection dbConn = db.ReadDataSource("trxn").getConnection();) {
                withdrawInfo = RabbitUtils.getWithdrawalRequest(withdraw.getTrxnId(), dbConn);
                if (null != withdrawInfo) {
                    custInfo = RabbitUtils.getCustomerDetails((long) withdrawInfo.get("profileId"), dbConn);
                }
            } catch (Exception e) {
            }

            if (null == withdrawInfo || null == custInfo) {
                logger.info(Utilities.getLogPreString("WithdrawalApp")
                        + "handleWithdrawal()"
                        + "|RequestId:" + withdraw.getTrxnId()
                        + "|Request NOT FOUND***Retry!!!");
                return false;
            }

            withdraw.setPhoneNumber(custInfo.get("msisdn").toString());
            withdraw.setProfileId((long) withdrawInfo.get("profileId"));
            if (!Utilities.FindInIntegerArray(basics.pendingWithdrawalStatus,
                    (int) withdrawInfo.get("status"))
                    || !Utilities.FindInIntegerArray(basics.pendingWithdrawalStatus,
                            (int) withdrawInfo.get("responseCode"))) {
                logger.info(Utilities.getLogPreString("WithdrawalApp")
                        + "handleWithdrawal()"
                        + "|RequestId:" + withdraw.getTrxnId()
                        + "|Msisdn:" + withdraw.getPhoneNumber()
                        + "|Request is already processed!");
                return true;
            }

            if (!ValidateSignature(withdraw.getSignature(),
                    (withdraw.getTrxnId() + "" + withdraw.getIp()
                    + "" + withdraw.getPaybillId() + "" + withdraw.getDate()
                    + "" + withdraw.getProfileId()))) {
                logger.warn(Utilities.getLogPreString("WithdrawalApp")
                        + "handleWithdrawal()"
                        + "|RequestId:" + withdraw.getTrxnId()
                        + "|Msisdn:" + withdraw.getPhoneNumber()
                        + "|Invalid Security signature!!!");

                return Queue.publishMessage(
                        new JSONObject()
                                .put("type", "WITHDRAWAL")
                                .put("failure_msg", "Invalid Withdrawal Security signature!!!")
                                .put("request", new JSONObject(message))
                                .toString(),
                        "WITHDRAWAL_FAILED",
                        "WITHDRAWAL_FAILED",
                        "WITHDRAWAL_FAILED",
                        null,
                        null);
            }

            withdraw.setAmount((double) withdrawInfo.get("amount"));
            withdraw.setCurrency((String) withdrawInfo.get("currency"));

            String source = "MPESA_WITHDRAWAL_VIA_DARAJA";
            if (withdraw.getChannel().equalsIgnoreCase("BROKER")) {
                source = "MPESA_WITHDRAWAL_VIA_BROKER";

                String withdrawalKey = "_WithdrawalViaBroker:Paybill**" + withdraw.getPaybillNumber();
                String redisData = JedisUtils.selectOneData(withdrawalKey);
                if (Utilities.isBlank(redisData)) {
                    logger.error(Utilities.getLogPreString("WithdrawalApp")
                            + "handleWithdrawal()"
                            + "|RequestId:" + withdraw.getTrxnId()
                            + "|Msisdn:" + withdraw.getPhoneNumber()
                            + "|Paybill:" + withdraw.getPaybillNumber()
                            + "|B2C paybill settings NOT found!!!");

                    return Queue.publishMessage(
                            new JSONObject()
                                    .put("type", "WITHDRAWAL")
                                    .put("failure_msg", "B2C paybill settings NOT found!!!")
                                    .put("request", new JSONObject(message))
                                    .toString(),
                            "WITHDRAWAL_FAILED",
                            "WITHDRAWAL_FAILED",
                            "WITHDRAWAL_FAILED",
                            null,
                            null);
                }

                try {
                    JSONObject wSettings = new JSONObject(redisData);
                    if (!Utilities.CreateMd5(wSettings.getString("ipAddress"))
                            .equalsIgnoreCase(withdraw.getIpAddress())) {
                        logger.error(Utilities.getLogPreString("WithdrawalApp")
                                + "handleWithdrawal()"
                                + "|RequestId:" + withdraw.getTrxnId()
                                + "|Msisdn:" + withdraw.getPhoneNumber()
                                + "|Paybill:" + withdraw.getPaybillNumber()
                                + "|B2C Paybill IpAddress signature  doesnt Match!!!!");

                        return Queue.publishMessage(
                                new JSONObject()
                                        .put("type", "WITHDRAWAL")
                                        .put("failure_msg", "B2C Paybill "
                                                + "IpAddress signature  doesnt Match!")
                                        .put("request", new JSONObject(message))
                                        .toString(),
                                "WITHDRAWAL_FAILED",
                                "WITHDRAWAL_FAILED",
                                "WITHDRAWAL_FAILED",
                                null,
                                null);
                    }

                    withdraw.setAppKey(wSettings.getString("appKey"));
                    withdraw.setCallBackURL(wSettings.getString("url").trim());
                    withdraw.setPaybillNumber(wSettings.getString("paybillCode"));
                } catch (Exception jse) {
                    logger.error(Utilities.getLogPreString("WithdrawalApp")
                            + "handleWithdrawal()"
                            + "|RequestId:" + withdraw.getTrxnId()
                            + "|Msisdn:" + withdraw.getPhoneNumber()
                            + "|Paybill:" + withdraw.getPaybillNumber()
                            + "|Exception:", jse);

                    return Queue.publishMessage(
                            new JSONObject()
                                    .put("type", "WITHDRAWAL")
                                    .put("failure_msg", "JSONException:" + jse.getMessage())
                                    .put("request", new JSONObject(message))
                                    .toString(),
                            "WITHDRAWAL_FAILED",
                            "WITHDRAWAL_FAILED",
                            "WITHDRAWAL_FAILED",
                            null,
                            null);
                }
            }

            long trxnId = CreateTransaction(
                    withdraw.getTrxnId(),
                    withdraw.getProfileId(),
                    withdraw.getCurrency(),
                    withdraw.getAmount(),
                    (double) withdrawInfo.get("charges"),
                    source, "MPESA Withdrawal from " + withdraw.getChannel()
                    + " Via Paybill#" + withdraw.getPaybillNumber(),
                    new JSONObject()
                            .put("msisdn", withdraw.getPhoneNumber())
                            .put("ip", withdraw.getIp())
                            .put("channel", withdraw.getChannel())
                            .put("referenceId", withdrawInfo.get("referenceId"))
                            .put("paybillId", withdraw.getPaybillId()));
            if (trxnId < 1) {
                if (trxnId == -1) {
                    logger.info(Utilities.getLogPreString("WithdrawalApp")
                            + "handleWithdrawal()"
                            + "|RequestId:" + withdraw.getTrxnId()
                            + "|Msisdn:" + withdraw.getPhoneNumber()
                            + "|Duplicate Transaction");
                    return true;
                }

                return false;
            }

            ZonedDateTime issuedAt = ZonedDateTime
                    .now(ZoneOffset.ofHours(3));

            JSONObject brokerPayload = new JSONObject()
                    .put("amount", withdraw.getAmount())
                    .put("msisdn", withdraw.getPhoneNumber())
                    .put("narration", "withdrawal")
                    .put("timestamp", issuedAt.toInstant().getEpochSecond())
                    .put("reference", "MB-" + withdrawInfo.get("referenceId")
                            + "-" + withdraw.getTrxnId())
                    .put("paybillCode", withdraw.getPaybillNumber());

            Map<String, String> httpHeader = new HashMap<>();
            httpHeader.put("X-App-Key", withdraw.getAppKey());
            httpHeader.put("X-Hash-Key", HashUtils.hashCreate(
                    brokerPayload.toString(),
                    withdraw.getAppKey()));

            logger.info(Utilities.getLogPreString("WithdrawalApp")
                    + "handleWithdrawal()"
                    + "|RequestId:" + withdraw.getTrxnId()
                    + "|Msisdn:" + withdraw.getPhoneNumber()
                    + "|HttpHeaders:" + httpHeader.toString()
                    + "|SendHttpJsonPostData()" + brokerPayload.toString());

            Instant httpTime = Instant.now();
            Map<String, Object> httpResponse = ApiCalls
                    .sendHttpJsonPostData(withdraw.getCallBackURL(),
                            brokerPayload.toString(),
                            httpHeader,
                            basics,
                            logger, true);
            logger.info(Utilities.getLogPreString("WithdrawalApp")
                    + "handleWithdrawal()"
                    + "|Took " + Utilities.CalculateTAT(httpTime) + " mSec(s)"
                    + "|RequestId:" + withdraw.getTrxnId()
                    + "|Msisdn:" + withdraw.getPhoneNumber()
                    + "|URL:" + Utilities.sequentialMask(withdraw.getCallBackURL(), 0.6, '#')
                    + "|SendHttpJsonPostData()" + httpResponse.toString());

            int responseCode = 3001;
            int responseStatus = 3001;

            JSONObject req;
            String resStr = httpResponse.get("response").toString();
            if (!Utilities.FindInIntegerArray(basics.succeessHttpStatuses,
                    (int) httpResponse.get("statusCode"))) {
                String conversationId = "";
                String OriginatorConversationID = "";
                String StatusDesc = "Request failed. ErrorCode:" + httpResponse.get("statusCode");
                if (!Utilities.isBlank(resStr)) {
                    try {
                        req = new JSONObject(resStr);
                        responseCode = req.getInt("StatusCode");
                        responseStatus = req.getInt("StatusCode");
                        StatusDesc = "Request failed. " + req.getString("StatusDesc");
                        StatusDesc += ". ErrorCode:" + httpResponse.get("statusCode");
                    } catch (Exception e) {
                    }
                } else {
                    if (!Utilities.isBlank(httpResponse.get("error").toString())) {
                        StatusDesc = "Request failed. " + httpResponse.get("error").toString();
                        StatusDesc += ". ErrorCode:" + httpResponse.get("statusCode");
                    }
                }

                long wId = CreateWithdrawalDlr(
                        withdraw.getTrxnId(),
                        responseCode,
                        responseStatus,
                        StatusDesc,
                        "F-" + withdrawInfo.get("referenceId"),
                        "", 0.0, 0.0,
                        0.0, withdraw.getIp(),
                        Utilities.now("yyyy-MM-dd HH:mm:ss"),
                        new JSONObject()
                                .put("trxnId", trxnId)
                                .put("msisdn", withdraw.getPhoneNumber())
                                .put("paybill", withdraw.getPaybillNumber()),
                        conversationId,
                        OriginatorConversationID,
                        withdraw.getAmount(),
                        (double) withdrawInfo.get("charges"),
                        withdraw.getProfileId(),
                        withdraw.getCurrency(),
                        trxnId);
                if (wId < 1) {
                    Queue.publishMessage(
                            new JSONObject()
                                    .put("type", "WITHDRAWAL")
                                    .put("failure_msg", "Failed to create Withdrawal Transaction")
                                    .put("request", new JSONObject(message))
                                    .put("response", new JSONObject(resStr))
                                    .toString(),
                            "WITHDRAWAL_FAILED",
                            "WITHDRAWAL_FAILED",
                            "WITHDRAWAL_FAILED",
                            null,
                            null);
                }

                processed = true;
            } else {
                String StatusDesc = "Request failed. Error!";

                String conversationId = "";
                String OriginatorConversationID = "";
                try {
                    req = new JSONObject(resStr);

                    responseCode = req.getInt("StatusCode");
                    responseStatus = req.getInt("StatusCode");
                    StatusDesc = req.getString("Status") + ". " + req.getString("StatusDesc");

                    if (req.has("ResponseData")) {
                        JSONObject ResponseData = req.getJSONObject("ResponseData");
                        if (ResponseData.has("Details")) {
                            JSONObject Details = ResponseData.getJSONObject("Details");
                            responseCode = Details.getInt("ResponseCode");
                            responseStatus = Details.getInt("ServiceStatus");
                            StatusDesc = Details.getString("ResponseDesc");
                            conversationId = Details.getString("ConversationID");
                            OriginatorConversationID = Details.getString("OriginatorConversationID");
                        }
                    }
                } catch (Exception e) {
                }

                long wId = CreateWithdrawalDlr(
                        withdraw.getTrxnId(), responseCode,
                        responseStatus, StatusDesc,
                        "F-" + withdrawInfo.get("referenceId"),
                        "", 0.0, 0.0,
                        0.0, withdraw.getIp(),
                        Utilities.now("yyyy-MM-dd HH:mm:ss"),
                        new JSONObject()
                                .put("trxnId", trxnId)
                                .put("msisdn", withdraw.getPhoneNumber())
                                .put("paybill", withdraw.getPaybillNumber()),
                        conversationId, OriginatorConversationID,
                        withdraw.getAmount(),
                        (double) withdrawInfo.get("charges"),
                        withdraw.getProfileId(),
                        withdraw.getCurrency(),
                        trxnId);
                if (wId < 1) {
                    Queue.publishMessage(
                            new JSONObject()
                                    .put("type", "WITHDRAWAL")
                                    .put("failure_msg", "Failed to create Withdrawal Transaction")
                                    .put("request", new JSONObject(message))
                                    .put("response", new JSONObject(resStr))
                                    .toString(),
                            "WITHDRAWAL_FAILED",
                            "WITHDRAWAL_FAILED",
                            "WITHDRAWAL_FAILED",
                            null,
                            null);
                }

                processed = true;
            }
        } catch (Exception e) {
            logger.error(Utilities.getLogPreString("WithdrawalApp")
                    + "handleWithdrawal()"
                    + "-" + queueName
                    + "|Exception:", e);
            processed = false;
        }

        return processed;
    }

    /**
     * CreateTransaction
     *
     * @param requestId
     * @param profileId
     * @param currency
     * @param amount
     * @param charges
     * @param source
     * @param desc
     * @param extraData
     * @return
     */
    private long CreateTransaction(long requestId, long profileId, String currency,
            double amount, double charges, String source, String desc, JSONObject extraData) {
        long trxnId = 0;
        try (final java.sql.Connection dbConn = db.WriteDataSource("trxn")
                .getConnection();) {
            String insertTrxnSql = "INSERT INTO transaction(profile_id"
                    + ",reference_type_id,transaction_type_id,reference_id"
                    + ",amount,currency,source,description,extra_data"
                    + ",created_at) VALUES(?,?,?,?,?,?,?,?,?,NOW())";
            dbConn.setAutoCommit(false);
            try (final PreparedStatement ps = dbConn.prepareStatement(
                    insertTrxnSql, PreparedStatement.RETURN_GENERATED_KEYS);
                    final PreparedStatement psx = dbConn.prepareStatement(
                            insertTrxnSql, PreparedStatement.RETURN_GENERATED_KEYS);) {
                ps.setLong(1, profileId);
                ps.setInt(2, 28);//WITHDRAWAL_MPESA_CHARGES
                ps.setInt(3, basics.TRANSACTION_TYPE_DEBIT_ID);
                ps.setLong(4, requestId);
                ps.setDouble(5, -charges);
                ps.setString(6, currency);
                ps.setString(7, "WITHDRAWAL_MPESA_CHARGES");
                ps.setString(8, "MPESA Withdrawal Charges for requestId:" + requestId);
                ps.setString(9, extraData.toString());
                long chargesId = ps.executeUpdate();
                try (ResultSet rs = ps.getGeneratedKeys()) {
                    if (rs.next()) {
                        chargesId = rs.getLong(1);
                    }
                }

                if (chargesId < 1) {
                    throw new SQLException("System Charges TrxnId creation failed "
                            + "|RequestId:" + requestId
                            + "|ProfileId:" + profileId);
                }

                psx.setLong(1, profileId);
                psx.setInt(2, 1);//WITHDRAWAL_MPESA
                psx.setInt(3, basics.TRANSACTION_TYPE_DEBIT_ID);
                psx.setLong(4, chargesId);
                psx.setDouble(5, -amount);
                psx.setString(6, currency);
                psx.setString(7, source);
                psx.setString(8, desc);
                psx.setString(9, extraData.toString());
                trxnId = psx.executeUpdate();
                try (ResultSet rs = ps.getGeneratedKeys()) {
                    if (rs.next()) {
                        trxnId = rs.getLong(1);
                    }
                }

                if (trxnId < 1) {
                    throw new SQLException("System TrxnId creation failed "
                            + "|RequestId:" + requestId
                            + "|ProfileId:" + profileId);
                }

                dbConn.commit();
            } catch (SQLIntegrityConstraintViolationException e) {
                if (e.getErrorCode() == 1062 || e.getSQLState().startsWith("23")) {
                    logger.error(Utilities.getLogPreString("WithdrawalApp")
                            + "CreateTransaction()"
                            + "|RequestId:" + requestId
                            + "|Duplicate‑entry / unique constraint violations"
                            + "|Exception:" + e.getMessage());
                    return -1;
                }

                throw e;
            } catch (SQLException e) {
                dbConn.rollback();
                throw e;
            }
        } catch (Exception e) {
            logger.error(Utilities.getLogPreString("WithdrawalApp")
                    + "CreateTransaction()"
                    + "|RequestId:" + requestId
                    + "|Exception:", e);
        }
        return trxnId;
    }

    /**
     * CreateWithdrawalDlr
     *
     * @param requestId
     * @param responseCode
     * @param responseStatus
     * @param responseDescription
     * @param receiptNumber
     * @param receiverName
     * @param transactionAmount
     * @param orgWorkingBalance
     * @param orgUtilityBalance
     * @param ipAddress
     * @param trxnCompletedDate
     * @param extraData
     * @param conversationId
     * @param originalConversationId
     * @param WithdrawAmount
     * @param charges
     * @param profileId
     * @param currency
     * @param transactionId
     * @return
     */
    private long CreateWithdrawalDlr(long requestId, int responseCode,
            int responseStatus, String responseDescription, String receiptNumber,
            String receiverName, double transactionAmount, double orgWorkingBalance,
            double orgUtilityBalance, String ipAddress, String trxnCompletedDate,
            JSONObject extraData, String conversationId, String originalConversationId,
            double WithdrawAmount, double charges, long profileId, String currency,
            long transactionId) {

        long id = 0;
        try (final java.sql.Connection dbConn = db.WriteDataSource("trxn").getConnection();
                final java.sql.Connection pConn = db.WriteDataSource("profile").getConnection();
                final PreparedStatement ps = dbConn.prepareStatement(
                        "INSERT withdrawal_dlr(request_id,response_code,response_status"
                        + ",response_description,receipt_number,receiver_name"
                        + ",transaction_amount,org_working_balance,org_utility_balance"
                        + ",extra_data,ip_address,transaction_completed_date,created_at) "
                        + "VALUES(?,?,?,?,?,?,?,?,?,?,?,?,NOW())", PreparedStatement.RETURN_GENERATED_KEYS);
                final PreparedStatement psUpdate = dbConn.prepareStatement(
                        "UPDATE withdrawal_request SET response_code=?,service_status=?"
                        + ",response_desc=?,conversation_id=?,originator_conversation_id=? "
                        + "WHERE id=? LIMIT 1")) {

            dbConn.setAutoCommit(false);

            ps.setLong(1, requestId);
            ps.setInt(2, ((responseCode == 0) ? 90000 : 90001));
            ps.setInt(3, ((responseStatus == 0) ? 90000 : 90001));
            ps.setString(4, responseDescription);
            ps.setString(5, receiptNumber);
            ps.setString(6, receiverName);
            ps.setDouble(7, transactionAmount);
            ps.setDouble(8, orgWorkingBalance);
            ps.setDouble(9, orgUtilityBalance);
            ps.setString(10, extraData.toString());
            ps.setString(11, ipAddress);
            ps.setString(12, trxnCompletedDate);
            id = ps.executeUpdate();
            try (ResultSet rs = ps.getGeneratedKeys()) {
                if (rs.next()) {
                    id = rs.getLong(1);
                }
            }

            if (id < 1) {
                dbConn.rollback();
                throw new SQLException("Withdrawal Dlr creation failed "
                        + "|RequestId:" + requestId);
            }

            psUpdate.setInt(1, responseCode);
            psUpdate.setInt(2, responseStatus);
            psUpdate.setString(3, responseDescription);
            psUpdate.setString(4, conversationId);
            psUpdate.setString(5, originalConversationId);
            psUpdate.setLong(6, requestId);
            int updateState = psUpdate.executeUpdate();
            if (updateState < 1) {
                dbConn.rollback();
                throw new SQLException("Withdrawal Dlr creation failed "
                        + "|RequestId:" + requestId);
            }

            if (responseCode != 0 || responseStatus != 0) {

                pConn.setAutoCommit(false);
                try (final PreparedStatement psBalance
                        = pConn.prepareStatement("UPDATE profile_balance SET balance=balance+?"
                                + ",bonus=bonus+? WHERE profile_id=? LIMIT 1");) {
                    psBalance.setDouble(1, WithdrawAmount + charges);
                    psBalance.setDouble(2, 0.0);
                    psBalance.setLong(3, profileId);
                    int updates = psBalance.executeUpdate();
                    if (updates < 1) {
                        pConn.rollback();
                        dbConn.rollback();
                        throw new SQLException("Profile Wallet Update failed " + profileId);
                    }
                } catch (Exception e) {
                    throw e;
                }

                String insertTrxnSql = "INSERT INTO transaction(profile_id"
                        + ",reference_type_id,transaction_type_id,reference_id"
                        + ",amount,currency,source,description,extra_data"
                        + ",created_at) VALUES(?,?,?,?,?,?,?,?,?,NOW())";
                try (final PreparedStatement psT = dbConn.prepareStatement(
                        insertTrxnSql, PreparedStatement.RETURN_GENERATED_KEYS);
                        final PreparedStatement psR = dbConn.prepareStatement(
                                insertTrxnSql, PreparedStatement.RETURN_GENERATED_KEYS);) {
                    psT.setLong(1, profileId);
                    psT.setInt(2, 3);//WITHDRAWAL_REVERSAL_MPESA
                    psT.setInt(3, basics.TRANSACTION_TYPE_REVERSAL_ID);
                    psT.setLong(4, transactionId);
                    psT.setDouble(5, WithdrawAmount);
                    psT.setString(6, currency);
                    psT.setString(7, "MPESA_WITHDRAWAL_REVERSAL");
                    psT.setString(8, "Reversal on Failed withdrawal. RequestId:" + requestId);
                    psT.setString(9, extraData.toString());
                    long trxnId = psT.executeUpdate();
                    try (ResultSet rs = psT.getGeneratedKeys()) {
                        if (rs.next()) {
                            trxnId = rs.getLong(1);
                        }
                    }

                    if (trxnId < 1) {
                        throw new SQLException("System TrxnId creation failed "
                                + "|RequestId:" + requestId
                                + "|ProfileId:" + profileId);
                    }

                    psR.setLong(1, profileId);
                    psR.setInt(2, 29);//WITHDRAWAL_REVERSAL_MPESA
                    psR.setInt(3, basics.TRANSACTION_TYPE_REVERSAL_ID);
                    psR.setLong(4, trxnId);
                    psR.setDouble(5, charges);
                    psR.setString(6, currency);
                    psR.setString(7, "MPESA_WITHDRAWAL_CHARGES_REVERSAL");
                    psR.setString(8, "Charges Reversal on Failed withdrawal. RequestId:" + requestId);
                    psR.setString(9, extraData.toString());
                    long trxnChargesId = psR.executeUpdate();
                    try (ResultSet rs = psR.getGeneratedKeys()) {
                        if (rs.next()) {
                            trxnChargesId = rs.getLong(1);
                        }
                    }

                    if (trxnChargesId < 1) {
                        throw new SQLException("System TrxnId creation failed "
                                + "|RequestId:" + requestId
                                + "|ProfileId:" + profileId);
                    }
                } catch (Exception e) {
                    pConn.rollback();
                    dbConn.rollback();
                    throw e;
                }

                pConn.commit();
            }
            dbConn.commit();
        } catch (Exception e) {
            logger.error(Utilities.getLogPreString("WithdrawalApp")
                    + "CreateWithdrawalDlr()"
                    + "|RequestId:" + requestId
                    + "|Exception:", e);
        }

        return id;
    }

    /**
     * ValidateSignature
     *
     * @param signature
     * @param signedStr
     * @return
     */
    private boolean ValidateSignature(String signature, String signedStr) {
        try {
            return Utilities.CreateMd5(signedStr + "" + props.withdrawalKey()).equals(signature);
        } catch (NoSuchAlgorithmException ex) {
            return false;
        }

    }
}

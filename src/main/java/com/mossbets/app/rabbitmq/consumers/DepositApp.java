package com.mossbets.app.rabbitmq.consumers;

import com.mossbets.app.rabbitmq.models.MpesaDeposits;
import com.mossbets.app.rabbitmq.utils.RabbitUtils;
import com.mossbets.app.resources.DB;
import com.mossbets.app.utils.Queue;
import com.mossbets.app.utils.Utilities;
import com.mossbets.app.utils.props.Basics;
import com.mossbets.app.utils.props.Props;
import com.rabbitmq.client.AMQP;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;
import com.rabbitmq.client.DefaultConsumer;
import com.rabbitmq.client.Envelope;
import io.quarkus.runtime.ShutdownEvent;
import io.quarkus.runtime.StartupEvent;
import io.quarkus.scheduler.Scheduled;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.event.Observes;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.Instant;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import org.codehaus.jettison.json.JSONArray;
import org.codehaus.jettison.json.JSONObject;
import org.jboss.logging.Logger;

@ApplicationScoped
public class DepositApp {

    @Inject
    Props props;

    @Inject
    Basics basics = new Basics();

    @Inject
    DB db;

    @Inject
    private Queue Queue;

    @Inject
    Logger logger;

    @Inject
    @Named("walletExecutor")
    ExecutorService es;

    @Inject
    ScheduledExecutorService scheduler;

    private Connection connection;
    private int reconnectAttempt = 0;
    private final int maxBackoffSeconds = 60;
    private volatile boolean running = true;

    void onStart(@Observes StartupEvent ev) {
        logger.info(Utilities.getLogPreString("DepositApp")
                + "onStart()"
                + "|Starting RabbitMQ Consumer...");
        scheduleReconnect(0);
    }

    void onStop(@Observes ShutdownEvent event) {
        logger.info(Utilities.getLogPreString("DepositApp")
                + "onStop()"
                + "|Application shutting down...");
        cleanup();
    }

    @PostConstruct
    void startOnAppBoot() {
        scheduleReconnect(0); // ✅ triggers startup
    }

    /**
     * scheduleReconnect
     *
     * @param delaySeconds
     */
    private void scheduleReconnect(int delaySeconds) {
        scheduler.schedule(this::connectWithBackoff, delaySeconds, TimeUnit.SECONDS);
    }

    /**
     * connectWithBackoff
     */
    private void connectWithBackoff() {
        if (!running) {
            return;
        }

        try {
            connectAndStartConsuming();
            logger.info(Utilities.getLogPreString("DepositApp")
                    + "connectWithBackoff()"
                    + "|RabbitMQ Consumer connected and consuming....");
            reconnectAttempt = 0; // reset on success
        } catch (Exception e) {
            reconnectAttempt++;
            int backoff = calculateBackoff(reconnectAttempt);
            logger.errorf(Utilities.getLogPreString("DepositApp")
                    + "connectWithBackoff()"
                    + "|Reconnect failed (attempt #%d). Retrying in %ds...",
                    reconnectAttempt, backoff);
            scheduleReconnect(backoff);
        }
    }

    /**
     * calculateBackoff
     *
     * @param attempt
     * @return
     */
    private int calculateBackoff(int attempt) {
        int base = (int) Math.min(Math.pow(2, attempt), maxBackoffSeconds);
        int jitter = ThreadLocalRandom.current().nextInt(0, 5);
        return base + jitter; // Exponential + jitter
    }

    /**
     * createFactory
     *
     * @return
     */
    private ConnectionFactory createFactory() {
        ConnectionFactory factory = new ConnectionFactory();
        factory.setHost(props.rabbitMqHost());
        factory.setPort(props.rabbitMqPort());
        factory.setVirtualHost(props.rabbitMqVhost());
        factory.setUsername(props.rabbitMqUsername());
        factory.setPassword(props.rabbitMqPassword());
        factory.setTopologyRecoveryEnabled(props.rabbitMqConRecovery());
        factory.setAutomaticRecoveryEnabled(props.rabbitMqTopologyRecovery());
        factory.setNetworkRecoveryInterval(props.rabbitMqNetworkRecovery());
        return factory;
    }

    /**
     * connectAndStartConsuming
     *
     * @throws Exception
     */
    private void connectAndStartConsuming() throws Exception {
        connection = createFactory().newConnection();
        connection.addShutdownListener(cause -> {
            logger.info(Utilities.getLogPreString("DepositApp")
                    + "connectAndStartConsuming()"
                    + "|Connection shutdown: " + cause.getMessage());
            if (running) {
                scheduleReconnect(0); // retry again
            }
        });

        for (String queue : props.DepositQueues()) {
            String queueName = props.rabbitMqPrefix().concat("_" + queue + "_QUEUE");
            String exchange = props.rabbitMqPrefix().concat("_" + queue + "_EXCHANGE");
            String route = props.rabbitMqPrefix().concat("_" + queue + "_ROUTE");

            Channel channel = connection.createChannel();
            channel.queueDeclare(queueName, true, false, false, null);
            channel.exchangeDeclare(exchange, "direct", true, false, null);
            channel.queueBind(queueName, exchange, route);
            channel.basicQos(props.rabbitMqPrefetchCount());
            channel.basicConsume(queueName, false, new DefaultConsumer(channel) {
                @Override
                @SuppressWarnings("UseSpecificCatch")
                public void handleDelivery(String consumerTag, Envelope envelope,
                        AMQP.BasicProperties properties, byte[] body) {
                    long deliveryTag = envelope.getDeliveryTag();
                    try {
                        es.submit(() -> handleMessage(body, queueName, channel, deliveryTag));
                    } catch (Exception ex) {
                        logger.error(Utilities.getLogPreString("DepositApp")
                                + "connectAndStartConsuming()"
                                + "|Queue " + queueName
                                + "|ConsumerTag:" + consumerTag
                                + "|Exception: " + ex.getMessage());
                        rejectAck(channel, deliveryTag, true);
                    }
                }
            });

            logger.infof(Utilities.getLogPreString("DepositApp")
                    + "connectAndStartConsuming()"
                    + "|Queue [%s]"
                    + "|Exchange [%s]"
                    + "|Route [%s]", queueName, exchange, route);
        }
    }

    /**
     * handleMessage
     *
     * @param body
     * @param queueName
     * @param channel
     * @param deliveryTag
     */
    @SuppressWarnings("UseSpecificCatch")
    private void handleMessage(byte[] body, String queueName, Channel channel, long deliveryTag) {
        Instant tat = Instant.now();
        try {
            if (handleDeposits(body, queueName)) {
                successAck(channel, deliveryTag);
            } else {
                rejectAck(channel, deliveryTag, true);
            }
        } catch (Exception e) {
            rejectAck(channel, deliveryTag, true);

            logger.error(Utilities.getLogPreString("DepositApp")
                    + "handleMessage()"
                    + "-" + queueName
                    + "|Took " + Utilities.CalculateTAT(tat) + " mSec(s)"
                    + "|Exception:" + e.getMessage());
        }
    }

    /**
     * handleDeposits
     *
     * @param body
     * @param queueName
     * @return
     */
    @SuppressWarnings("UseSpecificCatch")
    private boolean handleDeposits(byte[] body, String queueName) {

        boolean processed = false;
        String message = new String(body, StandardCharsets.UTF_8);
        try {
            logger.info(Utilities.getLogPreString("DepositApp")
                    + "handleDeposits()"
                    + "|Received"
                    + "-" + queueName
                    + "- Msg:" + message);

            MpesaDeposits deposit = new MpesaDeposits();
            try {
                JSONObject obj = new JSONObject(message);
                String TransType = "";
                if (obj.has("TransactionType")) {
                    TransType = obj.getString("TransactionType");
                }

                if (obj.has("TransType")) {
                    TransType = obj.getString("TransType");
                }

                deposit.setCurrency("KES");
                deposit.setMSISDN(obj.getString("MSISDN"));
                deposit.setTransID(obj.getString("TransID"));
                deposit.setBusinessShortCode(obj.getString("BusinessShortCode"));

                if (obj.getDouble("TransAmount") >= props.maxDeposit()) {
                    throw new IllegalArgumentException("Trans Amount is > " + props.maxDeposit()
                            + " for TransID:" + deposit.getTransID()
                            + " and MSISDN:" + deposit.getMSISDN());
                }

                if (obj.getDouble("TransAmount") < props.minDeposit()) {
                    throw new IllegalArgumentException("Trans Amount is < " + props.minDeposit()
                            + " for TransID:" + deposit.getTransID()
                            + " and MSISDN:" + deposit.getMSISDN());
                }

                deposit.setMerchantRequestID("");
                deposit.setThirdPartyTransID("");
                if (TransType.equalsIgnoreCase("Customer Payment Via OD")) {
                    String sign = obj.getLong("MSISDN")
                            + "" + deposit.getTransID()
                            + "" + obj.getDouble("TransAmount")
                            + "" + obj.getString("ThirdPartyTransID");
                    if (!Utilities.CreateMd5(sign)
                            .equals(obj.getString("Signature"))) {
                        logger.info(Utilities.getLogPreString("DepositApp")
                                + "handleDeposits()"
                                + "|Invalid Signature"
                                + "|" + TransType
                                + "|Q:" + queueName
                                + "|" + deposit.getTransID()
                                + "|" + deposit.getMSISDN()
                                + "|" + obj.getString("TransAmount"));

                        obj.put("RejectReason", "Invalid Signature");

                        String dfQueue = Utilities.getRandomValue(
                                props.depositFailedQueues());
                        return Queue.publishMessage(
                                obj.toString(),
                                dfQueue,
                                dfQueue,
                                dfQueue,
                                null,
                                null);
                    }

                    deposit.setFirstName(obj.getString("FirstName"));
                    deposit.setMiddleName(obj.getString("MiddleName"));
                    deposit.setLastName(obj.getString("LastName"));
                    deposit.setMerchantRequestID(obj.getString("MerchantRequestID"));
                    deposit.setThirdPartyTransID(obj.getString("ThirdPartyTransID"));
                } else {
                    String sign = deposit.getTransID()
                            + "" + deposit.getBusinessShortCode()
                            + "" + obj.getString("TransAmount")
                            + "" + obj.getString("IpAddress")
                            + "" + props.mpesaC2bBrokerKey();

                    String genSignature = Utilities.CreateMd5(sign);
                    if (!genSignature.equals(obj.getString("Signature"))) {
                        logger.info(Utilities.getLogPreString("DepositApp")
                                + "handleDeposits()"
                                + "|Invalid Signature >>" + sign
                                + "|GenSign:" + genSignature
                                + "|PaySign:" + obj.getString("Signature")
                                + "|" + TransType
                                + "|Q:" + queueName
                                + "|" + deposit.getTransID()
                                + "|" + deposit.getMSISDN()
                                + "|" + obj.getString("TransAmount"));

                        obj.put("RejectReason", "Invalid Signature");

                        String dfQueue = Utilities.getRandomValue(
                                props.depositFailedQueues());
                        return Queue.publishMessage(
                                message,
                                dfQueue,
                                dfQueue,
                                dfQueue,
                                null,
                                null);
                    }
                }

                deposit.setTransAmount(obj.getDouble("TransAmount"));
                deposit.setTransactionType(TransType);
                deposit.setBillRefNumber(obj.getString("BillRefNumber"));

                deposit.setOrgAccountBalance(obj.getDouble("OrgAccountBalance"));
                deposit.setTransTime(obj.getString("TransTime"));

                String firstName = "";
                String middleName = "";
                String lastName = "";
                if (obj.has("InvoiceNumber")) {
                    String invoiceNumber = obj.getString("InvoiceNumber");
                    if (!Utilities.isBlank(invoiceNumber)) {
                        String[] invArr = invoiceNumber.split("\\s+");

                        firstName = invArr.length > 0 ? invArr[0] : "";
                        middleName = invArr.length > 2 ? invArr[1] : "";
                        lastName = invArr.length > 2 ? invArr[2] : invArr.length > 1 ? invArr[1] : "";

                        deposit.setFirstName(firstName);
                        deposit.setMiddleName(middleName);
                        deposit.setLastName(lastName);
                    }
                }

                if (obj.has("KYCInfo")) {
                    if (TransType.equalsIgnoreCase("Organization To Organization Transfer")) {
                        JSONObject KYCInfo = obj.getJSONObject("KYCInfo");
                        String KYCName = KYCInfo.getString("KYCName");
                        String KYCValue = KYCInfo.getString("KYCValue");

                        if (KYCName.equalsIgnoreCase("Organization Name")
                                && KYCValue.equalsIgnoreCase("Airtel Mobile -Lipa Na M-PESA Interops")) {
                            if (!deposit.getMSISDN().equalsIgnoreCase(
                                    deposit.getBillRefNumber())) {
                                deposit.setMSISDN(Utilities.formatKenyanMsisdn(
                                        deposit.getBillRefNumber()));
                            }
                        }
                    } else {
                        JSONArray KYCInfo = obj.getJSONArray("KYCInfo");
                        for (int i = 0; i < KYCInfo.length(); i++) {
                            JSONObject kycObj = KYCInfo.getJSONObject(i);
                            String kycName = kycObj.getString("KYCName");
                            String kycValue = kycObj.getString("KYCValue");

                            if (kycName.equalsIgnoreCase("[Personal Details][First Name]")) {
                                deposit.setFirstName(kycValue);
                                continue;
                            }

                            if (kycName.equalsIgnoreCase("[Personal Details][Middle Name]")) {
                                deposit.setMiddleName(kycValue);
                                continue;
                            }

                            if (kycName.equalsIgnoreCase("[Personal Details][Last Name]")) {
                                deposit.setLastName(kycValue);
                                continue;
                            }

                            if (kycName.equalsIgnoreCase("Organization Name")) {
                                //deposit.setLastName(kycValue);
                            }
                        }
                    }
                }
            } catch (Exception js) {
                logger.error(Utilities.getLogPreString("DepositApp")
                        + "handleDeposits()"
                        + "-" + queueName
                        + "|Msg:" + message
                        + "|Exception:" + js.getMessage(), js);
                return true;
            }

            if (RabbitUtils.checkDuplicateMpesaTrxnExist(
                    deposit.getTransID(), db.WriteDataSource("trxn"))) {
                logger.info(Utilities.getLogPreString("DepositApp")
                        + "handleDeposits()"
                        + "|Transaction is a duplicate"
                        + "|" + deposit.getTransID()
                        + "|" + deposit.getMSISDN()
                        + "|" + deposit.getTransAmount());
                return true;
            }

            Map<String, Object> profile = null;
            try (final java.sql.Connection dbConn = db.ReadDataSource("profile").getConnection();) {
                profile = RabbitUtils.getMSISDNviaHash(deposit.getMSISDN(), dbConn);
            } catch (Exception e) {
            }

            if (null == profile) {
                logger.info(Utilities.getLogPreString("DepositApp")
                        + "handleDeposits()"
                        + "|Customer Profile NOT exists"
                        + "|" + deposit.getTransID()
                        + "|" + deposit.getMSISDN()
                        + "|" + deposit.getTransAmount());

                String dfQueue = Utilities.getRandomValue(props.depositFailedQueues());
                return Queue.publishMessage(
                        message,
                        dfQueue,
                        dfQueue,
                        dfQueue,
                        null,
                        null);
            }

            logger.info(Utilities.getLogPreString("DepositApp")
                    + "handleDeposits()"
                    + "|" + deposit.getTransID()
                    + "<<-->>" + profile.toString());

            String insertMpesaSql = "INSERT INTO payin_transaction(trxn_code,trxn_account"
                    + ",trxn_msisdn,trxn_sender,trx_currency,trxn_amount,trxn_paybill"
                    + ",trxn_org_balance,trxn_timestamp,trxn_repayment_type,trxn_extra_data"
                    + ",txn_ip_address,created_at) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,NOW())";

            String insertTrxnSql = "INSERT INTO transaction(profile_id,reference_type_id"
                    + ",transaction_type_id,reference_id,amount,currency,source,description"
                    + ",extra_data,created_at) VALUES(?,?,?,?,?,?,?,?,?,NOW())";

            int referenceTypeId = 2;//DEPOSIT_MPESA
            int transactionTypeId = 2;//CREDIT
            String source = "DEPOSIT_MPESA";

            double bonusAmount = 0.0;
            long profileId = (long) profile.get("profileId");
            try (java.sql.Connection dbW = db.WriteDataSource("trxn").getConnection();
                    java.sql.Connection dbP = db.WriteDataSource("profile").getConnection();) {

                String name = profile.get("name").toString();
                if (Utilities.isBlank((name))) {
                    if (!Utilities.isBlank(deposit.getFirstName())) {
                        name = deposit.getFirstName();
                    }

                    if (!Utilities.isBlank(deposit.getMiddleName())) {
                        name += " " + deposit.getMiddleName();
                    }

                    if (!Utilities.isBlank(deposit.getLastName())) {
                        name += " " + deposit.getLastName();
                    }

                    if (!Utilities.isBlank(name)) {
                        logger.info(Utilities.getLogPreString("DepositApp")
                                + "handleDeposits()"
                                + "|Updating Profile Name"
                                + "|" + deposit.getTransID()
                                + "|" + profileId
                                + "|" + name);
                        try (final PreparedStatement psName = dbP.prepareStatement(
                                "UPDATE `profile` SET `name`=? WHERE `id`=? LIMIT 1");) {
                            psName.setString(1, name.trim());
                            psName.setLong(2, profileId);
                            int updateState = psName.executeUpdate();
                            if (updateState < 1) {
                                throw new SQLException("Update Profile Name Update failed "
                                        + "|Q:" + queueName
                                        + "|" + deposit.getTransID()
                                        + "|" + deposit.getMSISDN()
                                        + "|" + deposit.getTransAmount());
                            }
                        } catch (SQLException e) {
                            dbW.rollback();
                            dbP.rollback();
                            throw e;
                        }
                    }
                }

                Map<String, Object> depoStats = RabbitUtils.getFirstDeposit(
                        profileId, referenceTypeId, transactionTypeId,
                        source, dbW);
                if ((int) depoStats.get("count") < 1) {
//                    if (deposit.getTransAmount() >= 10) {
//                        if ((deposit.getTransAmount() >= 10)
//                                && (deposit.getTransAmount() <= 49)) {
//                            bonusAmount = 4;
//                        } else if ((deposit.getTransAmount() >= 50)
//                                && (deposit.getTransAmount() <= 99)) {
//                            bonusAmount = 7;
//                        } else if ((deposit.getTransAmount() >= 100)
//                                && (deposit.getTransAmount() <= 999)) {
//                            bonusAmount = 24;
//                        } else if ((deposit.getTransAmount() >= 1000)
//                                && (deposit.getTransAmount() <= 2499)) {
//                            bonusAmount = 35;
//                        } else if ((deposit.getTransAmount() >= 2500)
//                                && (deposit.getTransAmount() <= 4999)) {
//                            bonusAmount = 57;
//                        } else if ((deposit.getTransAmount() >= 5000)
//                                && (deposit.getTransAmount() <= 9999)) {
//                            bonusAmount = 85;
//                        } else {
//                            bonusAmount = 112;
//                        }
//                    }
                }

                long paymentId = 0;
                dbW.setAutoCommit(false);
                dbP.setAutoCommit(false);
                try (final PreparedStatement ps = dbW.prepareStatement(
                        insertMpesaSql, PreparedStatement.RETURN_GENERATED_KEYS);) {
                    ps.setString(1, deposit.getTransID());
                    ps.setString(2, deposit.getBillRefNumber());
                    ps.setString(3, deposit.getMSISDN());
                    ps.setString(4, deposit.getFirstName()
                            + " " + deposit.getMiddleName()
                            + " " + deposit.getLastName());
                    ps.setString(5, deposit.getCurrency());
                    ps.setDouble(6, deposit.getTransAmount());
                    ps.setString(7, deposit.getBusinessShortCode());
                    ps.setDouble(8, deposit.getOrgAccountBalance());
                    ps.setString(9, deposit.getTransTime());
                    ps.setString(10, deposit.getTransactionType());
                    ps.setString(11, new JSONObject()
                            .put("merchantRequestID", deposit.getMerchantRequestID())
                            .put("thirdPartyTransID", deposit.getThirdPartyTransID())
                            .put("billReferenceNo", deposit.getBillRefNumber())
                            .put("msisdn", profile.get("msisdn"))
                            .toString());
                    ps.setString(12, "");
                    paymentId = ps.executeUpdate();
                    try (ResultSet rs = ps.getGeneratedKeys()) {
                        if (rs.next()) {
                            paymentId = rs.getLong(1);
                        }
                    }

                    if (paymentId < 1) {
                        throw new SQLException("Mpesa creation failed "
                                + "|Q:" + queueName
                                + "|" + deposit.getTransID()
                                + "|" + deposit.getMSISDN()
                                + "|" + deposit.getTransAmount());
                    }
                } catch (SQLException e) {
                    dbW.rollback();
                    throw e;
                }

                try (final PreparedStatement ps = dbW.prepareStatement(
                        insertTrxnSql, PreparedStatement.RETURN_GENERATED_KEYS);) {
                    ps.setLong(1, profileId);
                    ps.setInt(2, referenceTypeId);
                    ps.setInt(3, transactionTypeId);
                    ps.setLong(4, paymentId);
                    ps.setDouble(5, deposit.getTransAmount());
                    ps.setString(6, deposit.getCurrency());
                    ps.setString(7, source);
                    ps.setString(8, "MPESA deposit from Paybill:" + deposit.getBusinessShortCode());
                    ps.setString(9, new JSONObject()
                            .put("receiptNo", deposit.getTransID())
                            .put("msisdn", profile.get("msisdn"))
                            .put("accNo", profile.get("accNo"))
                            .put("orgBal", deposit.getOrgAccountBalance())
                            .put("bonusIssued", bonusAmount)
                            .toString());
                    long trxnId = ps.executeUpdate();
                    try (ResultSet rs = ps.getGeneratedKeys()) {
                        if (rs.next()) {
                            trxnId = rs.getLong(1);
                        }
                    }

                    if (trxnId < 1) {
                        throw new SQLException("System TrxnId creation failed "
                                + "|Q:" + queueName
                                + "|" + deposit.getTransID()
                                + "|" + deposit.getMSISDN()
                                + "|" + deposit.getTransAmount());
                    }
                } catch (SQLException e) {
                    dbW.rollback();
                    throw e;
                }

                long accountId = (long) profile.get("accId");
                if (accountId > 0) {
                    String updateBalanceSql = "UPDATE profile_balance SET balance=balance+?"
                            + ",bonus=bonus+? WHERE id=? LIMIT 1";
                    try (final PreparedStatement psBalance
                            = dbP.prepareStatement(updateBalanceSql);) {
                        psBalance.setDouble(1, deposit.getTransAmount());
                        psBalance.setDouble(2, bonusAmount);
                        psBalance.setLong(3, accountId);
                        int updateState = psBalance.executeUpdate();
                        if (updateState < 1) {
                            throw new SQLException("Profile Wallet Update failed "
                                    + "|Q:" + queueName
                                    + "|" + deposit.getTransID()
                                    + "|" + deposit.getMSISDN()
                                    + "|" + deposit.getTransAmount());
                        }
                    } catch (SQLException e) {
                        dbW.rollback();
                        dbP.rollback();
                        throw e;
                    }
                } else {
                    String createBalanceSql = "INSERT INTO profile_balance"
                            + "(profile_id,currency,balance,bonus,created_at) "
                            + "VALUES(?,?,?,?,NOW())";
                    try (final PreparedStatement ps = dbP.prepareStatement(
                            createBalanceSql, PreparedStatement.RETURN_GENERATED_KEYS);) {
                        ps.setLong(1, profileId);
                        ps.setString(2, deposit.getCurrency());
                        ps.setDouble(3, deposit.getTransAmount());
                        ps.setDouble(4, bonusAmount);
                        accountId = ps.executeUpdate();
                        try (ResultSet rs = ps.getGeneratedKeys()) {
                            if (rs.next()) {
                                accountId = rs.getLong(1);
                            }
                        }

                        if (accountId < 1) {
                            throw new SQLException("Profile Wallet creation failed "
                                    + "|Q:" + queueName
                                    + "|" + deposit.getTransID()
                                    + "|" + deposit.getMSISDN()
                                    + "|" + deposit.getTransAmount());
                        }
                    } catch (SQLException e) {
                        dbW.rollback();
                        dbP.rollback();
                        throw e;
                    }
                }

                String updateBalanceSql = "UPDATE profile_attribution "
                        + "SET first_deposit_date=IFNULL(NULLIF(first_deposit_date, NULL), NOW())"
                        + ",last_deposit_date=NOW(),deposit_count=deposit_count+1"
                        + ",total_deposits=total_deposits+?,total_bonus=total_bonus+? "
                        + "WHERE profile_id=? LIMIT 1";
                try (final PreparedStatement psUpdateAttribute
                        = dbP.prepareStatement(updateBalanceSql);) {
                    psUpdateAttribute.setDouble(1, deposit.getTransAmount());
                    psUpdateAttribute.setDouble(2, bonusAmount);
                    psUpdateAttribute.setLong(3, profileId);
                    int updateState = psUpdateAttribute.executeUpdate();
                    if (updateState < 1) {
                        throw new SQLException("Profile Attribution Wallet Update failed "
                                + "|Q:" + queueName
                                + "|" + deposit.getTransID()
                                + "|" + deposit.getMSISDN()
                                + "|" + deposit.getTransAmount());
                    }
                } catch (SQLException e) {
                    dbW.rollback();
                    dbP.rollback();
                    throw e;
                }

                /**
                 * The Excise Tax Implementation
                 */
                long summaryId = 0;
                try (PreparedStatement ps = dbW.prepareStatement(
                        "SELECT id FROM compliance_transaction_summary "
                        + "WHERE summary_date=DATE(NOW())");) {
                    try (ResultSet rs = ps.executeQuery()) {
                        if (rs.next()) {
                            summaryId = rs.getLong("id");
                        }
                    } catch (SQLException e) {
                        throw e;
                    }
                } catch (Exception e) {
                    dbW.rollback();
                    dbP.rollback();
                    throw e;
                }

                double exciseTax = props.taxExcise() * deposit.getTransAmount();

                if (summaryId < 1) {
                    try (PreparedStatement ps = dbW.prepareStatement(
                            "INSERT INTO compliance_transaction_summary"
                            + "(bets,deposit_count,stake,excirce_tax,summary_date"
                            + ",created_at) VALUES(?,?,?,DATE(NOW()),NOW())",
                            PreparedStatement.RETURN_GENERATED_KEYS);) {
                        ps.setInt(1, 0);
                        ps.setInt(2, 1);
                        ps.setDouble(3, deposit.getTransAmount());
                        ps.setDouble(4, exciseTax);
                        summaryId = ps.executeUpdate();
                        try (ResultSet rs = ps.getGeneratedKeys()) {
                            if (rs.next()) {
                                summaryId = rs.getLong(1);
                            }
                        }

                        if (summaryId < 1) {
                            throw new SQLException("Failed to create ExciseTax compliance_transaction_summary "
                                    + "|" + deposit.getTransID()
                                    + "|" + deposit.getMSISDN()
                                    + "|" + deposit.getTransAmount());
                        }
                    } catch (Exception e) {
                        dbW.rollback();
                        dbP.rollback();
                        throw e;
                    }
                } else {
                    try (PreparedStatement ps = dbW.prepareStatement(
                            "UPDATE compliance_transaction_summary SET "
                            + "deposit_count=deposit_count+1,stake=stake+?"
                            + ",excirce_tax=excirce_tax+? WHERE id=? LIMIT 1");) {
                        ps.setDouble(1, deposit.getTransAmount());
                        ps.setDouble(2, exciseTax);
                        ps.setLong(3, summaryId);
                        int updateState = ps.executeUpdate();
                        if (updateState < 1) {
                            throw new SQLException("Failed to Update ExciseTax compliance_transaction_summary "
                                    + "|" + deposit.getTransID()
                                    + "|" + deposit.getMSISDN()
                                    + "|" + deposit.getTransAmount());
                        }
                    } catch (Exception e) {
                        dbW.rollback();
                        dbP.rollback();
                        throw e;
                    }
                }

                dbW.commit();
                dbP.commit();
            } catch (Exception e) {
                processed = false;
                throw e;
            }

            String messageOut = "You have deposited " + deposit.getCurrency() + ":" + deposit.getTransAmount()
                    + "\nMPESA REF. " + deposit.getTransID()
                    + "\n-\n"
                    + "Play now: https://mossbets.com"
                    + "\nHelp:" + basics.HelpLine;

            if (bonusAmount > 0) {
                messageOut = "You have deposited " + deposit.getCurrency() + ":" + deposit.getTransAmount()
                        + "\nMPESA REF. " + deposit.getTransID()
                        + "\n-\n"
                        + "CASHBACK " + deposit.getCurrency() + ":" + bonusAmount
                        + "\n-\n"
                        + "Play now: https://mossbets.com"
                        + "\nHelp:" + basics.HelpLine;
            }

            if (!Utilities.isBlank(deposit.getFirstName())) {
                messageOut = deposit.getFirstName() + ", " + messageOut;
            }

            try {
                long outboxId = RabbitUtils.createSMSOutBox(profileId,
                        basics.SenderId, messageOut, "DEPOSIT",
                        db.WriteDataSource("profile"));

                String smsQ = Utilities.getRandomValue(props.SmsOutboxQueue());
                Queue.publishMessage(
                        new JSONObject()
                                .put("campaign_id", outboxId)
                                .put("message_pages", 1)
                                .put("message", messageOut)
                                .put("sms-id", "QUICKSENDVERIFICATION")
                                .put("network_regex", 1)
                                .put("network", profile.get("network").toString())
                                .put("alert_type", "TRANSACTIONAL")
                                .put("recipients", profile.get("msisdn"))
                                .put("outbox_id", outboxId)
                                .put("short_code", basics.SenderId)
                                .put("gateway", 1)
                                .put("dlr_url", "")
                                .put("auth_token", "auth_token_api")
                                .put("date_created", Utilities.now("yyyy-MM-dd HH:mm:ss"))
                                .toString(),
                        smsQ,
                        smsQ,
                        smsQ,
                        null,
                        null);
            } catch (Exception e) {
                logger.error(Utilities.getLogPreString("DepositApp")
                        + "handleDeposits()"
                        + "|Send Outbox"
                        + "|" + deposit.getTransID()
                        + "|Exception:" + e.getMessage());
            }

            processed = true;
        } catch (Exception e) {
            logger.error(Utilities.getLogPreString("DepositApp")
                    + "handleDeposits()"
                    + "-" + queueName
                    + "|Last Exception:", e);
            processed = false;
        }

        return processed;
    }

    /**
     * cleanup
     */
    @PreDestroy
    @SuppressWarnings("UseSpecificCatch")
    void cleanup() {
        logger.info(Utilities.getLogPreString("DepositApp")
                + "cleanup()"
                + "|Shutting DOWN RabbitMQ Consumer gracefully...");
        running = false;
        try {
            if (connection != null && connection.isOpen()) {
                connection.close();
            }

            logger.info(Utilities.getLogPreString("DepositApp")
                    + "cleanup()"
                    + "|RabbitMQ Connection closed");
        } catch (Exception e) {
            logger.fatal(Utilities.getLogPreString("DepositApp")
                    + "cleanup()"
                    + "|Exception", e);
        }
    }

    /**
     * isHealthy
     *
     * @return
     */
    public boolean isHealthy() {
        return connection != null && connection.isOpen();
    }

    /**
     * reconnectWithDelay
     */
    private void reconnectWithDelay() {
        reconnectAttempt++;
        int delay = calculateBackoff(reconnectAttempt);

        logger.warnf(Utilities.getLogPreString("DepositApp")
                + "reconnectWithDelay()"
                + "|Scheduling Reconnection in %d seconds (attempt #%d)...",
                delay, reconnectAttempt);

        scheduler.schedule(this::connectWithBackoff, delay, TimeUnit.SECONDS);
    }

    @Scheduled(every = "10m", concurrentExecution = Scheduled.ConcurrentExecution.SKIP)
    public void checkIfAppIsRunning() {
        if (isHealthy()) {
            logger.info(Utilities.getLogPreString("DepositApp")
                    + "AppChecks() => ALIVE");
        } else if (running) {
            logger.info(Utilities.getLogPreString("DepositApp")
                    + "AppChecks()"
                    + "|RabbitMQ Connection is DOWN. Attempting to reconnect...");
            reconnectWithDelay();
        }
    }

    /**
     * acknowledge
     *
     * @param channel
     * @param deliveryTag
     * @throws IOException
     */
    public void successAck(Channel channel, long deliveryTag) throws IOException {
        try {
            if (channel != null && channel.isOpen()) {
                channel.basicAck(deliveryTag, false);
                logger.infof(Utilities.getLogPreString("DepositApp")
                        + "successAck()"
                        + "|ACK [%s] tag:%d", "Success", deliveryTag);
            }
        } catch (IOException e) {
            throw e;
        }
    }

    /**
     *
     * @param channel
     * @param deliveryTag
     * @param requeue
     */
    public void rejectAck(Channel channel, long deliveryTag, boolean requeue) {
        try {
            if (channel != null && channel.isOpen()) {
                channel.basicReject(deliveryTag, requeue);
                logger.infof(Utilities.getLogPreString("DepositApp")
                        + "rejectAck()"
                        + "|ACK [%s] tag:%d", "Reject", deliveryTag);
            }
        } catch (IOException e) {
            logger.error(Utilities.getLogPreString("DepositApp")
                    + "rejectAck()"
                    + "IOException:" + e.getMessage()
                    + "|ACK Failed tag:" + deliveryTag);
        }
    }
}

package com.mossbets.app.rabbitmq.consumers;

import com.mossbets.app.rabbitmq.models.Refunds;
import com.mossbets.app.rabbitmq.utils.RabbitUtils;
import com.mossbets.app.resources.DB;
import com.mossbets.app.utils.Utilities;
import com.mossbets.app.utils.props.Basics;
import com.mossbets.app.utils.props.Props;
import com.rabbitmq.client.*;
import io.quarkus.runtime.ShutdownEvent;
import io.quarkus.runtime.StartupEvent;
import io.quarkus.scheduler.Scheduled;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.event.Observes;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.Instant;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import org.codehaus.jettison.json.JSONObject;
import org.jboss.logging.Logger;

@ApplicationScoped
public class RefundsApp {

    @Inject
    Props props;

    @Inject
    Basics basics = new Basics();

    @Inject
    DB db;

    @Inject
    Logger logger;

    @Inject
    @Named("refundExecutor")
    ExecutorService es;

    @Inject
    ScheduledExecutorService scheduler;

    private Connection connection;
    private int reconnectAttempt = 0;
    private final int maxBackoffSeconds = 60;
    private volatile boolean running = true;

    void onStart(@Observes StartupEvent ev) {
        logger.info(Utilities.getLogPreString("RefundsApp")
                + "onStart()"
                + "|Starting RefundsApp Consumer...");
        scheduleReconnect(0);
    }

    void onStop(@Observes ShutdownEvent event) {
        logger.info(Utilities.getLogPreString("RefundsApp")
                + "onStop()"
                + "|Application shutting down...");
        cleanup();
    }

    @PostConstruct
    void startOnAppBoot() {
        scheduleReconnect(0); // ✅ triggers startup
    }

    /**
     * scheduleReconnect
     *
     * @param delaySeconds
     */
    private void scheduleReconnect(int delaySeconds) {
        scheduler.schedule(this::connectWithBackoff, delaySeconds, TimeUnit.SECONDS);
    }

    /**
     * connectWithBackoff
     */
    private void connectWithBackoff() {
        if (!running) {
            return;
        }

        try {
            connectAndStartConsuming();
            logger.info(Utilities.getLogPreString("RefundsApp")
                    + "connectWithBackoff()"
                    + "|RefundsApp Consumer connected and consuming....");
            reconnectAttempt = 0; // reset on success
        } catch (Exception e) {
            reconnectAttempt++;
            int backoff = calculateBackoff(reconnectAttempt);
            logger.errorf(Utilities.getLogPreString("RefundsApp")
                    + "connectWithBackoff()"
                    + "|Reconnect failed (attempt #%d). Retrying in %ds...",
                    reconnectAttempt, backoff);
            scheduleReconnect(backoff);
        }
    }

    /**
     * calculateBackoff
     *
     * @param attempt
     * @return
     */
    private int calculateBackoff(int attempt) {
        int base = (int) Math.min(Math.pow(2, attempt), maxBackoffSeconds);
        int jitter = ThreadLocalRandom.current().nextInt(0, 5);
        return base + jitter; // Exponential + jitter
    }

    /**
     * createFactory
     *
     * @return
     */
    private ConnectionFactory createFactory() {
        ConnectionFactory factory = new ConnectionFactory();
        factory.setHost(props.rabbitMqHost());
        factory.setPort(props.rabbitMqPort());
        factory.setVirtualHost(props.rabbitMqVhost());
        factory.setUsername(props.rabbitMqUsername());
        factory.setPassword(props.rabbitMqPassword());
        factory.setTopologyRecoveryEnabled(props.rabbitMqConRecovery());
        factory.setAutomaticRecoveryEnabled(props.rabbitMqTopologyRecovery());
        factory.setNetworkRecoveryInterval(props.rabbitMqNetworkRecovery());
        return factory;
    }

    /**
     * connectAndStartConsuming
     *
     * @throws Exception
     */
    private void connectAndStartConsuming() throws Exception {
        connection = createFactory().newConnection();
        connection.addShutdownListener(cause -> {
            logger.info(Utilities.getLogPreString("RefundsApp")
                    + "connectAndStartConsuming()"
                    + "|Connection shutdown: " + cause.getMessage());
            if (running) {
                scheduleReconnect(0); // retry again
            }
        });

        for (String queue : props.RefundQueues()) {
            String queueName = props.rabbitMqPrefix().concat("_" + queue + "_QUEUE");
            String exchange = props.rabbitMqPrefix().concat("_" + queue + "_EXCHANGE");
            String route = props.rabbitMqPrefix().concat("_" + queue + "_ROUTE");

            Channel channel = connection.createChannel();
            channel.queueDeclare(queueName, true, false, false, null);
            channel.exchangeDeclare(exchange, "direct", true, false, null);
            channel.queueBind(queueName, exchange, route);
            channel.basicQos(props.rabbitMqPrefetchCount());
            channel.basicConsume(queueName, false, new DefaultConsumer(channel) {
                @Override
                @SuppressWarnings("UseSpecificCatch")
                public void handleDelivery(String consumerTag, Envelope envelope,
                        AMQP.BasicProperties properties, byte[] body) {
                    long deliveryTag = envelope.getDeliveryTag();
                    try {
                        es.submit(() -> handleMessage(
                                body, queueName, channel, deliveryTag));
                    } catch (Exception ex) {
                        logger.error(Utilities.getLogPreString("RefundsApp")
                                + "connectAndStartConsuming()"
                                + "|Queue " + queueName
                                + "|Exception: " + ex.getMessage());
                        rejectAck(channel, deliveryTag, true);
                    }
                }
            });

            logger.infof(Utilities.getLogPreString("RefundsApp")
                    + "connectAndStartConsuming()"
                    + "|Queue [%s]"
                    + "|Exchange [%s]"
                    + "|Route [%s]", queueName, exchange, route);
        }
    }

    /**
     * handleMessage
     *
     * @param body
     * @param queueName
     * @param channel
     * @param deliveryTag
     */
    @SuppressWarnings("UseSpecificCatch")
    private void handleMessage(byte[] body, String queueName, Channel channel, long deliveryTag) {
        try {
            if (processRefund(body, queueName)) {
                successAck(channel, deliveryTag);
            } else {
                rejectAck(channel, deliveryTag, true);
            }
        } catch (Exception e) {
            rejectAck(channel, deliveryTag, true);

            logger.error(Utilities.getLogPreString("RefundsApp")
                    + "handleMessage()"
                    + "-" + queueName
                    + "|Exception:" + e.getMessage());
        }
    }

    /**
     * RefundProcessor
     *
     * @param body
     * @param queueName
     * @return
     */
    @SuppressWarnings("UseSpecificCatch")
    private boolean processRefund(byte[] body, String queueName) {
        Instant tat = Instant.now();

        boolean processed = false;
        String message = new String(body, StandardCharsets.UTF_8);
        try {
            logger.info(Utilities.getLogPreString("RefundsApp")
                    + "processRefund()"
                    + "|Received"
                    + "-" + queueName
                    + "- Msg:" + message);

            Refunds refunds;
            try {
                refunds = new Refunds();
                JSONObject obj = new JSONObject(message);
                refunds.setType(obj.getString("type"));
                refunds.setBetId(obj.getLong("bet_id"));
                refunds.setTrxnId(obj.getLong("trxn_id"));
                refunds.setCustomerId(obj.getLong("customer_id"));
                refunds.setBetAmount(obj.getDouble("bet_amount"));
                refunds.setBetReference(obj.getString("bet_reference"));
                refunds.setRefundReason(obj.getString("reason"));
                refunds.setIpAddress(obj.getString("ip_address"));
                refunds.setSignature(obj.getString("signature"));
                refunds.setTimestamp(obj.getString("timestamp"));
            } catch (Exception js) {
                logger.error(Utilities.getLogPreString("RefundsApp")
                        + "processRefund()"
                        + "-" + queueName
                        + "|Took " + Utilities.CalculateTAT(tat) + " mSec(s)"
                        + "|Exception:" + js.getMessage());
                return true;
            }

            Map<String, Object> betInfo = null;
            Map<String, Double> betLimits = null;
            try (final java.sql.Connection dbConn = db.ReadDataSource("bets").getConnection();) {
                if (refunds.getType().equalsIgnoreCase("SPORTS")) {
                    betInfo = RabbitUtils.getSportsBetDetails(refunds.getBetId(), dbConn);
                    betLimits = RabbitUtils.getBetLimits((int) betInfo.get("clientId"),
                            betInfo.get("currency").toString(), "GENIUS SPORTS", dbConn);
                }
            } catch (Exception e) {
            }

            if (null == betInfo) {
                logger.info(Utilities.getLogPreString("RefundsApp")
                        + "processRefund()"
                        + "|BetId:" + refunds.getBetId()
                        + "|Bet NOT FOUND!!!");
                return true;
            }

            if (Utilities.FindInIntegerArray(basics.finalBetStatus,
                    (int) betInfo.get("status"))) {
                if ((long) betInfo.get("creditId") > 0) {
                    logger.info(Utilities.getLogPreString("RefundsApp")
                            + "processRefund()"
                            + "|BetId:" + refunds.getBetId()
                            + "|Bet Already processed!!!" + betInfo.toString());
                    return true;
                }
            }

            double stake = (double) betInfo.get("stake");

            int transTypeId = 7;//REFUND
            int referenceTypeId = 20;//WALLET_REFUND
            String source = "REFUND";
            String provider = "REFUND_PAYOUT";
            String table = "sports_bet";

            double bonus = 0;//bonus
            double balance = stake;
            double riskAmount = betLimits.get("riskAmount");

            JSONObject eData = new JSONObject(betInfo.get("eData").toString());
            if (refunds.getType().equalsIgnoreCase("SPORTS")) {
                referenceTypeId = 16;//SPORTS_BOOK_BET_REFUND
                provider = "SPORTS_BOOK_BET_REFUND";
                source = "SPORTS_BOOK_BET_REFUND";

                if (Utilities.FindInIntegerArray(new int[]{0, 1}, (int) betInfo.get("betType"))) {
                    if (eData.has("bonus")) {
                        JSONObject bonusObj = eData.getJSONObject("bonus");
                        if (bonusObj.has("bonus_amount")) {
                            bonus = bonusObj.getDouble("bonus_amount");
                        }

                        if (bonusObj.has("cash_amount")) {
                            balance = bonusObj.getDouble("cash_amount");
                        }
                    }
                }
            }

            try (final java.sql.Connection pDb = db.WriteDataSource("profile").getConnection();
                    final java.sql.Connection betDb = db.WriteDataSource("bets").getConnection();
                    final java.sql.Connection tDb = db.WriteDataSource("trxn").getConnection();) {

                pDb.setAutoCommit(false);
                betDb.setAutoCommit(false);
                tDb.setAutoCommit(false);

                long betCreditId = 0;
                try (final PreparedStatement ps = tDb.prepareStatement(
                        "INSERT INTO transaction(profile_id,reference_type_id"
                        + ",transaction_type_id,reference_id,amount"
                        + ",currency,source,description,extra_data,created_at) "
                        + "VALUES(?,?,?,?,?,?,?,?,?,NOW())",
                        PreparedStatement.RETURN_GENERATED_KEYS);) {
                    ps.setLong(1, refunds.getCustomerId());
                    ps.setInt(2, referenceTypeId);
                    ps.setInt(3, transTypeId);
                    ps.setLong(4, (long) betInfo.get("debitId"));
                    ps.setDouble(5, stake);
                    ps.setString(6, betInfo.get("currency").toString());
                    ps.setString(7, source);
                    ps.setString(8, refunds.getRefundReason());
                    ps.setString(9, new JSONObject()
                            .put("ip", refunds.getIpAddress())
                            .put("total_events", (int) betInfo.get("totalGames"))
                            .put("bet_id", refunds.getBetId())
                            .toString());
                    betCreditId = ps.executeUpdate();
                    try (ResultSet rs = ps.getGeneratedKeys()) {
                        if (rs.next()) {
                            betCreditId = rs.getLong(1);
                        }
                    }

                    if (betCreditId < 1) {
                        tDb.rollback();
                        throw new SQLException("Failed to create refund "
                                + "transactions:" + refunds.getBetId());
                    }

                    if (Utilities.FindInIntegerArray(new int[]{0, 1},
                            (int) betInfo.get("betType"))) {
                        if (balance >= riskAmount) {
                            logger.info(Utilities.getLogPreString("RefundsApp")
                                    + "processRefund()"
                                    + "|BetId:" + refunds.getBetId()
                                    + "| " + betInfo.get("currency") + ":" + stake
                                    + "|Will create a Transaction that will require approvals");

                            try (final PreparedStatement psAp = tDb.prepareStatement(
                                    "INSERT INTO transaction_pending_approval("
                                    + "profile_id,transaction_id,amount,currency"
                                    + ",source,description,created_at) "
                                    + "VALUES(?,?,?,?,?,?,NOW())",
                                    PreparedStatement.RETURN_GENERATED_KEYS);) {
                                String desc = "REFUNDS(s) Above "
                                        + betInfo.get("currency") + "." + riskAmount;
                                if (bonus > 0) {
                                    desc += ". Bonus of " + betInfo.get("currency") + ":" + bonus;
                                }

                                if (balance > 0) {
                                    desc += ". Balance of " + betInfo.get("currency") + ":" + bonus;
                                }

                                psAp.setLong(1, refunds.getCustomerId());
                                psAp.setLong(2, betCreditId);
                                psAp.setDouble(3, balance);
                                psAp.setString(4, betInfo.get("currency").toString());
                                psAp.setString(5, provider);
                                psAp.setString(6, desc);
                                long approvalId = psAp.executeUpdate();
                                try (ResultSet rs = psAp.getGeneratedKeys()) {
                                    if (rs.next()) {
                                        approvalId = rs.getLong(1);
                                    }
                                }

                                if (approvalId < 1) {
                                    tDb.rollback();
                                    throw new SQLException("Failed to create refund "
                                            + "approval:" + refunds.getBetId());
                                }

                            } catch (SQLException e) {
                                throw e;
                            }
                        }
                    }
                } catch (SQLException e) {
                    tDb.rollback();
                    throw e;
                }

                try (final PreparedStatement ps = betDb.prepareStatement(
                        "UPDATE " + table + " SET bet_credit_transaction_id=? "
                        + "WHERE bet_id=? AND bet_credit_transaction_id IS NULL LIMIT 1")) {
                    ps.setLong(1, betCreditId);
                    ps.setLong(2, refunds.getBetId());
                    int updateState = ps.executeUpdate();
                    if (updateState < 1) {
                        throw new SQLException("Failed to update BetId:" + refunds.getBetId());
                    }
                } catch (SQLException e) {
                    tDb.rollback();
                    betDb.rollback();
                    throw e;
                }

                if (eData.has("issued")) {
                    JSONObject issuedObj = eData.getJSONObject("issued");
                    if (issuedObj.has("id") && issuedObj.has("bnxId")) {
                        try (final PreparedStatement ps = pDb.prepareStatement(
                                "UPDATE profile_bonus SET status=? WHERE id=? "
                                + "AND bonus_id=? AND status=1 AND redeemed_on is NULL LIMIT 1")) {
                            ps.setInt(1, 1);
                            ps.setLong(2, issuedObj.getLong("id"));
                            ps.setInt(3, issuedObj.getInt("bnxId"));
                            int updateState = ps.executeUpdate();
                            if (updateState < 1) {
                                throw new SQLException("Failed to update freebet bonus "
                                        + "for BetId:" + refunds.getBetId());
                            }
                        } catch (SQLException e) {
                            pDb.rollback();
                            tDb.rollback();
                            betDb.rollback();
                            throw e;
                        }
                    }
                }

                if ((int) betInfo.get("betType") == 2) {
                    try (final PreparedStatement ps = pDb.prepareStatement(
                            "UPDATE profile_bonus SET status=?,redeemed_on=NULL "
                            + "WHERE id=? AND profile_id=? LIMIT 1")) {
                        ps.setInt(1, 1);
                        ps.setLong(2, eData.getJSONObject("bonus").getLong("bonus_id"));
                        ps.setLong(3, refunds.getCustomerId());
                        int updateState = ps.executeUpdate();
                        if (updateState < 1) {
                            throw new SQLException("Failed to update freebet bonus "
                                    + "for BetId:" + refunds.getBetId());
                        }
                    } catch (SQLException e) {
                        pDb.rollback();
                        tDb.rollback();
                        betDb.rollback();
                        throw e;
                    }
                } else {
                    try (final PreparedStatement ps = pDb.prepareStatement(
                            "UPDATE profile_balance SET balance=balance+?,bonus=bonus+? "
                            + "WHERE profile_id=? LIMIT 1")) {
                        ps.setDouble(1, balance);
                        ps.setDouble(2, bonus);
                        ps.setLong(3, refunds.getCustomerId());
                        int updateState = ps.executeUpdate();
                        if (updateState < 1) {
                            throw new SQLException("Failed to update Wallet for BetId:" + refunds.getBetId());
                        }
                    } catch (SQLException e) {
                        pDb.rollback();
                        tDb.rollback();
                        betDb.rollback();
                        throw e;
                    }
                }

                try (final PreparedStatement ps = pDb.prepareStatement(
                        "UPDATE profile_attribution SET total_refunds=total_refunds+?"
                        + ",last_refund_date=NOW() WHERE profile_id=? LIMIT 1")) {
                    ps.setDouble(1, stake);
                    ps.setLong(2, refunds.getCustomerId());
                    int updateState = ps.executeUpdate();
                    if (updateState < 1) {
                        throw new SQLException("Failed to update "
                                + "attribution for BetId:" + refunds.getBetId());
                    }
                } catch (SQLException e) {
                    pDb.rollback();
                    tDb.rollback();
                    betDb.rollback();
                    throw e;
                }

                pDb.commit();
                tDb.commit();
                betDb.commit();

                processed = true;
            } catch (Exception e) {
                throw e;
            }
        } catch (Exception e) {
            logger.error(Utilities.getLogPreString("RefundsApp")
                    + "processRefund()"
                    + "-" + queueName
                    + "|Took " + Utilities.CalculateTAT(tat) + " mSec(s)"
                    + "|Exception:", e);
            processed = false;
        }

        return processed;
    }

    /**
     * cleanup
     */
    @PreDestroy
    @SuppressWarnings("UseSpecificCatch")
    void cleanup() {
        logger.info(Utilities.getLogPreString("RefundsApp")
                + "cleanup()"
                + "|Shutting DOWN RabbitMQ Consumer gracefully...");
        running = false;
        try {
            if (connection != null && connection.isOpen()) {
                connection.close();
            }

            logger.info(Utilities.getLogPreString("RefundsApp")
                    + "cleanup()"
                    + "|RabbitMQ Connection closed");
        } catch (Exception e) {
            logger.fatal(Utilities.getLogPreString("RefundsApp")
                    + "cleanup()"
                    + "|Exception", e);
        }
    }

    /**
     * isHealthy
     *
     * @return
     */
    public boolean isHealthy() {
        return connection != null && connection.isOpen();
    }

    /**
     * reconnectWithDelay
     */
    private void reconnectWithDelay() {
        reconnectAttempt++;
        int delay = calculateBackoff(reconnectAttempt);

        logger.warnf(Utilities.getLogPreString("RefundsApp")
                + "reconnectWithDelay()"
                + "|Scheduling Reconnection in %d seconds (attempt #%d)...",
                delay, reconnectAttempt);

        scheduler.schedule(this::connectWithBackoff, delay, TimeUnit.SECONDS);
    }

    @Scheduled(every = "10m", concurrentExecution = Scheduled.ConcurrentExecution.SKIP)
    public void checkIfAppIsRunning() {
        if (isHealthy()) {
            logger.info(Utilities.getLogPreString("RefundsApp")
                    + "AppChecks() => ALIVE");
        } else if (running) {
            logger.info(Utilities.getLogPreString("RefundsApp")
                    + "AppChecks()"
                    + "|RabbitMQ Connection is DOWN. Attempting to reconnect...");
            reconnectWithDelay();
        }
    }

    /**
     * acknowledge
     *
     * @param channel
     * @param deliveryTag
     * @throws IOException
     */
    public void successAck(Channel channel, long deliveryTag) throws IOException {
        try {
            if (channel != null && channel.isOpen()) {
                channel.basicAck(deliveryTag, false);
                logger.infof(Utilities.getLogPreString("RefundsApp")
                        + "successAck()"
                        + "|ACK [%s] tag:%d", "Success", deliveryTag);
            }
        } catch (IOException e) {
            throw e;
        }
    }

    /**
     *
     * @param channel
     * @param deliveryTag
     * @param requeue
     */
    public void rejectAck(Channel channel, long deliveryTag, boolean requeue) {
        try {
            if (channel != null && channel.isOpen()) {
                channel.basicReject(deliveryTag, requeue);
                logger.infof(Utilities.getLogPreString("RefundsApp")
                        + "rejectAck()"
                        + "|ACK [%s] tag:%d", "Reject", deliveryTag);
            }
        } catch (IOException e) {
            logger.error(Utilities.getLogPreString("RefundsApp")
                    + "rejectAck()"
                    + "IOException:" + e.getMessage()
                    + "|ACK Failed tag:" + deliveryTag);
        }
    }
}

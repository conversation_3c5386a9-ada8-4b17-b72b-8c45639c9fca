package com.mossbets.app.rabbitmq.consumers;

import com.mossbets.app.rabbitmq.models.MpesaStk;
import com.mossbets.app.rabbitmq.utils.RabbitUtils;
import com.mossbets.app.resources.DB;
import com.mossbets.app.utils.ApiCalls;
import com.mossbets.app.utils.JedisUtils;
import com.mossbets.app.utils.Utilities;
import com.mossbets.app.utils.props.Basics;
import com.mossbets.app.utils.props.Props;
import com.rabbitmq.client.AMQP;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;
import com.rabbitmq.client.DefaultConsumer;
import com.rabbitmq.client.Envelope;
import io.quarkus.runtime.ShutdownEvent;
import io.quarkus.runtime.StartupEvent;
import io.quarkus.scheduler.Scheduled;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.event.Observes;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.Date;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import org.apache.commons.codec.binary.Base64;
import org.apache.http.HttpVersion;
import org.apache.http.client.HttpResponseException;
import org.apache.http.client.fluent.Async;
import org.apache.http.client.fluent.Content;
import org.apache.http.client.fluent.Request;
import org.apache.http.concurrent.FutureCallback;
import org.codehaus.jettison.json.JSONException;
import org.codehaus.jettison.json.JSONObject;
import org.jboss.logging.Logger;

@ApplicationScoped
public class StkDepostApp {

    @Inject
    Props props;

    @Inject
    Basics basics = new Basics();

    @Inject
    DB db;

    @Inject
    Logger logger;

    @Inject
    @Named("stkExecutor")
    ExecutorService es;

    @Inject
    ScheduledExecutorService scheduler;

    private Connection connection;
    private int reconnectAttempt = 0;
    private final int maxBackoffSeconds = 60;
    private volatile boolean running = true;

    void onStart(@Observes StartupEvent ev) {
        logger.info(Utilities.getLogPreString("StkDepostApp")
                + "onStart()"
                + "|Starting RabbitMQ Consumer...");
        scheduleReconnect(0);
    }

    void onStop(@Observes ShutdownEvent event) {
        logger.info(Utilities.getLogPreString("StkDepostApp")
                + "onStop()"
                + "|Application shutting down...");
        cleanup();
    }

    @PostConstruct
    void startOnAppBoot() {
        scheduleReconnect(0); // ✅ triggers startup
    }

    /**
     * scheduleReconnect
     *
     * @param delaySeconds
     */
    private void scheduleReconnect(int delaySeconds) {
        scheduler.schedule(this::connectWithBackoff, delaySeconds, TimeUnit.SECONDS);
    }

    /**
     * connectWithBackoff
     */
    private void connectWithBackoff() {
        if (!running) {
            return;
        }

        try {
            connectAndStartConsuming();
            logger.info(Utilities.getLogPreString("StkDepostApp")
                    + "connectWithBackoff()"
                    + "|RabbitMQ Consumer connected and consuming....");
            reconnectAttempt = 0; // reset on success
        } catch (Exception e) {
            reconnectAttempt++;
            int backoff = calculateBackoff(reconnectAttempt);
            logger.errorf(Utilities.getLogPreString("StkDepostApp")
                    + "connectWithBackoff()"
                    + "|Reconnect failed (attempt #%d). Retrying in %ds...",
                    reconnectAttempt, backoff);
            scheduleReconnect(backoff);
        }
    }

    /**
     * calculateBackoff
     *
     * @param attempt
     * @return
     */
    private int calculateBackoff(int attempt) {
        int base = (int) Math.min(Math.pow(2, attempt), maxBackoffSeconds);
        int jitter = ThreadLocalRandom.current().nextInt(0, 5);
        return base + jitter; // Exponential + jitter
    }

    /**
     * createFactory
     *
     * @return
     */
    private ConnectionFactory createFactory() {
        ConnectionFactory factory = new ConnectionFactory();
        factory.setHost(props.rabbitMqHost());
        factory.setPort(props.rabbitMqPort());
        factory.setVirtualHost(props.rabbitMqVhost());
        factory.setUsername(props.rabbitMqUsername());
        factory.setPassword(props.rabbitMqPassword());
        factory.setTopologyRecoveryEnabled(props.rabbitMqConRecovery());
        factory.setAutomaticRecoveryEnabled(props.rabbitMqTopologyRecovery());
        factory.setNetworkRecoveryInterval(props.rabbitMqNetworkRecovery());
        return factory;
    }

    /**
     * connectAndStartConsuming
     *
     * @throws Exception
     */
    private void connectAndStartConsuming() throws Exception {
        connection = createFactory().newConnection();
        connection.addShutdownListener(cause -> {
            logger.info(Utilities.getLogPreString("StkDepostApp")
                    + "connectAndStartConsuming()"
                    + "|Connection shutdown: " + cause.getMessage());
            if (running) {
                scheduleReconnect(0); // retry again
            }
        });

        for (String queue : props.StkCheckoutQueues()) {
            String queueName = props.rabbitMqPrefix().concat("_" + queue + "_QUEUE");
            String exchange = props.rabbitMqPrefix().concat("_" + queue + "_EXCHANGE");
            String route = props.rabbitMqPrefix().concat("_" + queue + "_ROUTE");

            Channel channel = connection.createChannel();
            channel.queueDeclare(queueName, true, false, false, null);
            channel.exchangeDeclare(exchange, "direct", true, false, null);
            channel.queueBind(queueName, exchange, route);
            channel.basicQos(props.rabbitMqPrefetchCount());
            channel.basicConsume(queueName, false, new DefaultConsumer(channel) {
                @Override
                @SuppressWarnings("UseSpecificCatch")
                public void handleDelivery(String consumerTag, Envelope envelope,
                        AMQP.BasicProperties properties, byte[] body) {
                    long deliveryTag = envelope.getDeliveryTag();
                    try {
                        es.submit(() -> handleMessage(body, queueName, channel, deliveryTag));
                    } catch (Exception ex) {
                        logger.error(Utilities.getLogPreString("StkDepostApp")
                                + "connectAndStartConsuming()"
                                + "|Queue " + queueName
                                + "|ConsumerTag:" + consumerTag
                                + "|Exception: " + ex.getMessage());
                        rejectAck(channel, deliveryTag, true);
                    }
                }
            });

            logger.infof(Utilities.getLogPreString("StkDepostApp")
                    + "connectAndStartConsuming()"
                    + "|Queue [%s]"
                    + "|Exchange [%s]"
                    + "|Route [%s]", queueName, exchange, route);
        }
    }

    /**
     * handleMessage
     *
     * @param body
     * @param queueName
     * @param channel
     * @param deliveryTag
     */
    @SuppressWarnings("UseSpecificCatch")
    private void handleMessage(byte[] body, String queueName, Channel channel, long deliveryTag) {
        Instant tat = Instant.now();
        try {
            if (handleStk(body, queueName)) {
                successAck(channel, deliveryTag);
            } else {
                rejectAck(channel, deliveryTag, true);
            }

            logger.info(Utilities.getLogPreString("StkDepostApp")
                    + "handleMessage()"
                    + "-" + queueName
                    + "|Took " + Utilities.CalculateTAT(tat) + " mSec(s)"
                    + "|Tag:" + deliveryTag);
        } catch (Exception e) {
            rejectAck(channel, deliveryTag, true);

            logger.error(Utilities.getLogPreString("StkDepostApp")
                    + "handleMessage()"
                    + "-" + queueName
                    + "|Took " + Utilities.CalculateTAT(tat) + " mSec(s)"
                    + "|Exception:" + e.getMessage());
        }
    }

    /**
     * handleStk
     *
     * @param body
     * @param queueName
     * @return
     */
    @SuppressWarnings("UseSpecificCatch")
    private boolean handleStk(byte[] body, String queueName) {

        boolean processed = false;
        String message = new String(body, StandardCharsets.UTF_8);
        try {
            logger.info(Utilities.getLogPreString("StkDepostApp")
                    + "handleStk()"
                    + "|Received"
                    + "-" + queueName
                    + "- Msg:" + message);

            MpesaStk stk = new MpesaStk();
            try {
                JSONObject obj = new JSONObject(message);
                stk.setAmount(obj.getDouble("amount"));
                stk.setCallBackURL(obj.getString("callBackURL"));
                stk.setPhoneNumber(obj.getString("phoneNumber"));
                stk.setPaybill(obj.getString("paybill"));
                stk.setTransactionDesc(obj.getString("transactionDesc"));
            } catch (Exception js) {
                logger.error(Utilities.getLogPreString("StkDepostApp")
                        + "handleStk()"
                        + "|Exception:" + js.getMessage());
                return true;
            }

            if (stk.getAmount() > 250000 || stk.getAmount() < 10) {
                logger.error(Utilities.getLogPreString("StkDepostApp")
                        + "handleStk()"
                        + "|" + stk.getAmount()
                        + "|" + stk.getPhoneNumber()
                        + "|Paybill [" + stk.getPaybill() + "]"
                        + "|Invalid Amount!");
                return true;
            }

            Map<String, String> pbConfig = null;
            String serviceKey = "Mossbets:PaybillSettings:Paybiil$" + stk.getPaybill();
            String rData = JedisUtils.selectOneData(serviceKey);
            if (!Utilities.isBlank(rData)) {
                try {
                    pbConfig = new JSONObject(rData).toMap();
                } catch (Exception e) {
                }
            }

            if (null == pbConfig) {
                pbConfig = RabbitUtils.getPaybillSettings(
                        serviceKey,
                        stk.getPaybill(),
                        db.ReadDataSource("trxn"));
                if (null == pbConfig) {
                    logger.error(Utilities.getLogPreString("StkDepostApp")
                            + "handleStk()"
                            + "-" + queueName
                            + "|" + stk.getPhoneNumber()
                            + "|Paybill [" + stk.getPaybill() + "] is NOT active!");
                    return true;
                }
            }

            if (props.mpesaC2bStkStatus() == 1) {
                return false;
            }

            String accessKey
                    = getSafAuthKey(
                            stk.getPaybill(),
                            pbConfig.get("secretKey"),
                            pbConfig.get("consumerKey"));
            if (Utilities.isBlank(accessKey)) {
                return false;
            }

            String timestamp = new SimpleDateFormat("yyyyMMddHHmmss", Locale.ENGLISH)
                    .format(new Date());

            byte[] encodedBytes = Base64.encodeBase64(stk.getPaybill()
                    .concat(pbConfig.get("passKey"))
                    .concat(timestamp).getBytes());

            Map<String, String> httpHeader = new HashMap<>();
            httpHeader.put("Content-Type", "application/json");
            httpHeader.put("Authorization", "Bearer " + accessKey);

            Instant httpTime = Instant.now();
            Map<String, Object> httpResponse = ApiCalls.sendHttpJsonPostData(props.mpesaSafCheckoutUrl(),
                    new JSONObject()
                            .put("BusinessShortCode", stk.getPaybill())
                            .put("Password", new String(encodedBytes))
                            .put("Timestamp", timestamp)
                            .put("Amount", stk.getAmount())
                            .put("PartyA", stk.getPhoneNumber())
                            .put("PartyB", stk.getPaybill())
                            .put("PhoneNumber", stk.getPhoneNumber())
                            .put("CallBackURL", stk.getCallBackURL())
                            .put("AccountReference", stk.getTransactionDesc())
                            .put("TransactionDesc", pbConfig.get("type"))
                            .put("TransactionType", pbConfig.get("type"))
                            .toString(),
                    httpHeader,
                    basics,
                    logger, false);
            logger.info(Utilities.getLogPreString("StkDepostApp")
                    + "handleStk()"
                    + "|Took " + Utilities.CalculateTAT(httpTime) + " mSec(s)"
                    + "|URL:" + props.mpesaSafCheckoutUrl()
                    + "|" + stk.getPhoneNumber()
                    + "|SendHttpJsonPostData()" + httpResponse.toString());

            int httpCode = (int) httpResponse.get("statusCode");
            String resStr = httpResponse.get("response").toString();

            if (!Utilities.FindInIntegerArray(basics.succeessHttpStatuses, httpCode)
                    || Utilities.isBlank(resStr)) {
                if (httpCode == 401) {
                    return true;
                }

                if (Utilities.isBlank(resStr)) {
                    return true;
                }

                if (!Utilities.isBlank(resStr)) {
                    JSONObject objres = new JSONObject(resStr);
                    String errorCode = "";
                    if (objres.has("errorCode")) {
                        errorCode = objres.getString("errorCode");
                    }

                    if (errorCode.equalsIgnoreCase("************")) {
                        return true;
                    }
                }

                return false;
            }

            int responseCode = 1;
            JSONObject objres = new JSONObject(resStr);
            if (objres.has("ResponseCode")) {
                responseCode = objres.getInt("ResponseCode");
            }

            if (responseCode == 0) {
                return true;
            }
        } catch (Exception e) {
            logger.error(Utilities.getLogPreString("StkDepostApp")
                    + "handleStk()"
                    + "-" + queueName
                    + "|Exception:", e);
            processed = false;
        }

        return processed;
    }

    /**
     * GetSafaricomAuthKey
     *
     * @param paybillNumber
     * @return
     */
    @SuppressWarnings("UseSpecificCatch")
    private String getSafAuthKey(String paybillNumber, String secretKey, String consumerkey) {
        String accessToken = null;
        try {
            String redisKey = "MpesaC2BPaybill$" + paybillNumber;
            String redisData = JedisUtils.selectOneData(redisKey);
            if (!Utilities.isBlank(redisData)) {
                try {
                    JSONObject obj = new JSONObject(redisData);
                    if (obj.has("access_token")) {
                        return obj.getString("access_token");
                    }
                } catch (JSONException e) {
                }
            }

            String result = sendGetRequest(secretKey, consumerkey);
            logger.info(Utilities.getLogPreString("StkDepostApp")
                    + "getSafAuthKey()"
                    + "| PaybillNumber:" + paybillNumber
                    + "| Response:" + result);
            if (null == result || result.isEmpty()) {
                return accessToken;
            }

            JSONObject obj = new JSONObject(result);
            if (obj.has("access_token")) {
                accessToken = obj.getString("access_token");

                JedisUtils.saveData(
                        redisKey,
                        new JSONObject()
                                .put("access_token", accessToken)
                                .toString(),
                        (60 * 55));
            }
        } catch (Exception ex) {
            accessToken = "";
            logger.error(Utilities.getLogPreString("StkDepostApp")
                    + "getSafAuthKey()"
                    + "| PaybillNumber:" + paybillNumber
                    + "| Exception Occured:" + ex.getMessage());
        }

        return accessToken;
    }

    /**
     * sendGetRequest
     *
     * @param secretKey
     * @param consumerKey
     * @return
     * @throws URISyntaxException
     * @throws InterruptedException
     * @throws IOException
     * @throws ExecutionException
     * @throws HttpResponseException
     */
    public String sendGetRequest(String secretKey, String consumerKey) throws URISyntaxException,
            InterruptedException, IOException, ExecutionException, HttpResponseException {
        URI requestURL = URI.create(props.mpesaSafAuthUrl());
        byte[] encodedBytes = Base64.encodeBase64(
                (consumerKey + ":" + secretKey).getBytes());

        ExecutorService esx = Executors.newSingleThreadExecutor();
        Async async = Async.newInstance().use(esx);
        final Request request = Request.Get(requestURL);
        request.connectTimeout(props.connectTimeOut());
        request.socketTimeout(props.socketTimeOut());
        request.version(HttpVersion.HTTP_1_1);
        request.userAgent("MOSSBETS_CHECKOUT/v1");
        request.addHeader("Authorization", "Basic " + new String(encodedBytes));
        Future<Content> future = async.execute(request, new FutureCallback<Content>() {
            @SuppressWarnings("override")
            public void failed(final Exception e) {
                logger.error(Utilities.getLogPreString("StkDepostApp")
                        + "SendGetRequest()"
                        + "|Request Failed:" + request
                        + "| Exception:" + e.getMessage());
            }

            @SuppressWarnings("override")
            public void completed(final Content content) {
                logger.info(Utilities.getLogPreString("StkDepostApp")
                        + "SendGetRequest()"
                        + "|Request completed:" + request);
            }

            @SuppressWarnings("override")
            public void cancelled() {
                logger.error(Utilities.getLogPreString("StkDepostApp")
                        + "SendGetRequest()"
                        + "|Request cancelled:" + request);
            }
        });
        esx.shutdown();
        return future.get().asString();
    }

    /**
     * cleanup
     */
    @PreDestroy
    @SuppressWarnings("UseSpecificCatch")
    void cleanup() {
        logger.info(Utilities.getLogPreString("StkDepostApp")
                + "cleanup()"
                + "|Shutting DOWN RabbitMQ Consumer gracefully...");
        running = false;
        try {
            if (connection != null && connection.isOpen()) {
                connection.close();
            }

            logger.info(Utilities.getLogPreString("StkDepostApp")
                    + "cleanup()"
                    + "|RabbitMQ Connection closed");
        } catch (Exception e) {
            logger.fatal(Utilities.getLogPreString("StkDepostApp")
                    + "cleanup()"
                    + "|Exception", e);
        }
    }

    /**
     * isHealthy
     *
     * @return
     */
    public boolean isHealthy() {
        return connection != null && connection.isOpen();
    }

    /**
     * reconnectWithDelay
     */
    private void reconnectWithDelay() {
        reconnectAttempt++;
        int delay = calculateBackoff(reconnectAttempt);

        logger.warnf(Utilities.getLogPreString("StkDepostApp")
                + "reconnectWithDelay()"
                + "|Scheduling Reconnection in %d seconds (attempt #%d)...",
                delay, reconnectAttempt);

        scheduler.schedule(this::connectWithBackoff, delay, TimeUnit.SECONDS);
    }

    @Scheduled(every = "10m", concurrentExecution = Scheduled.ConcurrentExecution.SKIP)
    public void checkIfAppIsRunning() {
        if (isHealthy()) {
            logger.info(Utilities.getLogPreString("StkDepostApp")
                    + "AppChecks() => ALIVE");
        } else if (running) {
            logger.info(Utilities.getLogPreString("StkDepostApp")
                    + "AppChecks()"
                    + "|RabbitMQ Connection is DOWN. Attempting to reconnect...");
            reconnectWithDelay();
        }
    }

    /**
     * acknowledge
     *
     * @param channel
     * @param deliveryTag
     * @throws IOException
     */
    public void successAck(Channel channel, long deliveryTag) throws IOException {
        try {
            if (channel != null && channel.isOpen()) {
                channel.basicAck(deliveryTag, false);
                logger.infof(Utilities.getLogPreString("StkDepostApp")
                        + "successAck()"
                        + "|ACK [%s] tag:%d", "Success", deliveryTag);
            }
        } catch (IOException e) {
            throw e;
        }
    }

    /**
     *
     * @param channel
     * @param deliveryTag
     * @param requeue
     */
    public void rejectAck(Channel channel, long deliveryTag, boolean requeue) {
        try {
            if (channel != null && channel.isOpen()) {
                channel.basicReject(deliveryTag, requeue);
                logger.infof(Utilities.getLogPreString("StkDepostApp")
                        + "rejectAck()"
                        + "|ACK [%s] tag:%d", "Reject", deliveryTag);
            }
        } catch (IOException e) {
            logger.error(Utilities.getLogPreString("StkDepostApp")
                    + "rejectAck()"
                    + "IOException:" + e.getMessage()
                    + "|ACK Failed tag:" + deliveryTag);
        }
    }
}

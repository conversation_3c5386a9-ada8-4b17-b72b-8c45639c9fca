package com.mossbets.app.rabbitmq.consumers;

import com.mossbets.app.rabbitmq.models.SmsOutbox;
import com.mossbets.app.utils.ApiCalls;
import com.mossbets.app.utils.Utilities;
import com.mossbets.app.utils.props.Basics;
import com.mossbets.app.utils.props.Props;
import com.rabbitmq.client.AMQP;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;
import com.rabbitmq.client.DefaultConsumer;
import com.rabbitmq.client.Envelope;
import io.quarkus.runtime.ShutdownEvent;
import io.quarkus.runtime.StartupEvent;
import io.quarkus.scheduler.Scheduled;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.event.Observes;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import org.codehaus.jettison.json.JSONObject;
import org.jboss.logging.Logger;

@ApplicationScoped
public class SMSApp {

    @Inject
    Props props;

    @Inject
    Basics basics = new Basics();

    @Inject
    Logger logger;

    @Inject
    @Named("smsExecutor")
    ExecutorService es;

    @Inject
    ScheduledExecutorService scheduler;

    private Connection connection;
    private int reconnectAttempt = 0;
    private final int maxBackoffSeconds = 60;
    private volatile boolean running = true;

    void onStart(@Observes StartupEvent ev) {
        logger.info(Utilities.getLogPreString("SMSApp")
                + "onStart()"
                + "|Starting RabbitMQ Consumer...");
        scheduleReconnect(0);
    }

    void onStop(@Observes ShutdownEvent event) {
        logger.info(Utilities.getLogPreString("SMSApp")
                + "onStop()"
                + "|Application shutting down...");
        cleanup();
    }

    @PostConstruct
    void startOnAppBoot() {
        scheduleReconnect(0); // ✅ triggers startup
    }

    /**
     * scheduleReconnect
     *
     * @param delaySeconds
     */
    private void scheduleReconnect(int delaySeconds) {
        scheduler.schedule(this::connectWithBackoff, delaySeconds, TimeUnit.SECONDS);
    }

    /**
     * connectWithBackoff
     */
    private void connectWithBackoff() {
        if (!running) {
            return;
        }

        try {
            connectAndStartConsuming();
            logger.info(Utilities.getLogPreString("SMSApp")
                    + "connectWithBackoff()"
                    + "|RabbitMQ Consumer connected and consuming....");
            reconnectAttempt = 0; // reset on success
        } catch (Exception e) {
            reconnectAttempt++;
            int backoff = calculateBackoff(reconnectAttempt);
            logger.errorf(Utilities.getLogPreString("SMSApp")
                    + "connectWithBackoff()"
                    + "|Reconnect failed (attempt #%d). Retrying in %ds...",
                    reconnectAttempt, backoff);
            scheduleReconnect(backoff);
        }
    }

    /**
     * calculateBackoff
     *
     * @param attempt
     * @return
     */
    private int calculateBackoff(int attempt) {
        int base = (int) Math.min(Math.pow(2, attempt), maxBackoffSeconds);
        int jitter = ThreadLocalRandom.current().nextInt(0, 5);
        return base + jitter; // Exponential + jitter
    }

    /**
     * createFactory
     *
     * @return
     */
    private ConnectionFactory createFactory() {
        ConnectionFactory factory = new ConnectionFactory();
        factory.setHost(props.rabbitMqHost());
        factory.setPort(props.rabbitMqPort());
        factory.setVirtualHost(props.rabbitMqVhost());
        factory.setUsername(props.rabbitMqUsername());
        factory.setPassword(props.rabbitMqPassword());
        factory.setTopologyRecoveryEnabled(props.rabbitMqConRecovery());
        factory.setAutomaticRecoveryEnabled(props.rabbitMqTopologyRecovery());
        factory.setNetworkRecoveryInterval(props.rabbitMqNetworkRecovery());
        return factory;
    }

    /**
     * connectAndStartConsuming
     *
     * @throws Exception
     */
    private void connectAndStartConsuming() throws Exception {
        connection = createFactory().newConnection();
        connection.addShutdownListener(cause -> {
            logger.info(Utilities.getLogPreString("SMSApp")
                    + "connectAndStartConsuming()"
                    + "|Connection shutdown: " + cause.getMessage());
            if (running) {
                scheduleReconnect(0); // retry again
            }
        });

        for (String queue : props.SmsOutboxQueue()) {
            String queueName = props.rabbitMqPrefix().concat("_" + queue + "_QUEUE");
            String exchange = props.rabbitMqPrefix().concat("_" + queue + "_EXCHANGE");
            String route = props.rabbitMqPrefix().concat("_" + queue + "_ROUTE");

            Channel channel = connection.createChannel();
            channel.queueDeclare(queueName, true, false, false, null);
            channel.exchangeDeclare(exchange, "direct", true, false, null);
            channel.queueBind(queueName, exchange, route);
            channel.basicQos(props.rabbitMqPrefetchCount());
            channel.basicConsume(queueName, false, new DefaultConsumer(channel) {
                @Override
                @SuppressWarnings("UseSpecificCatch")
                public void handleDelivery(String consumerTag, Envelope envelope,
                        AMQP.BasicProperties properties, byte[] body) {
                    long deliveryTag = envelope.getDeliveryTag();
                    try {
                        es.submit(() -> handleMessage(body, queueName, channel, deliveryTag));
                    } catch (Exception ex) {
                        logger.error(Utilities.getLogPreString("SMSApp")
                                + "connectAndStartConsuming()"
                                + "|Queue " + queueName
                                + "|ConsumerTag:" + consumerTag
                                + "|Exception: " + ex.getMessage());
                        rejectAck(channel, deliveryTag, true);
                    }
                }
            });

            logger.infof(Utilities.getLogPreString("SMSApp")
                    + "connectAndStartConsuming()"
                    + "|Queue [%s]"
                    + "|Exchange [%s]"
                    + "|Route [%s]", queueName, exchange, route);
        }
    }

    /**
     * isHealthy
     *
     * @return
     */
    public boolean isHealthy() {
        return connection != null && connection.isOpen();
    }

    /**
     * reconnectWithDelay
     */
    private void reconnectWithDelay() {
        reconnectAttempt++;
        int delay = calculateBackoff(reconnectAttempt);

        logger.warnf(Utilities.getLogPreString("SMSApp")
                + "reconnectWithDelay()"
                + "|Scheduling Reconnection in %d seconds (attempt #%d)...",
                delay, reconnectAttempt);

        scheduler.schedule(this::connectWithBackoff, delay, TimeUnit.SECONDS);
    }

    @Scheduled(every = "10m", concurrentExecution = Scheduled.ConcurrentExecution.SKIP)
    public void checkIfAppIsRunning() {
        if (isHealthy()) {
            logger.info(Utilities.getLogPreString("SMSApp")
                    + "AppChecks() => ALIVE");
        } else if (running) {
            logger.info(Utilities.getLogPreString("SMSApp")
                    + "AppChecks()"
                    + "|RabbitMQ Connection is DOWN. Attempting to reconnect...");
            reconnectWithDelay();
        }
    }

    /**
     * acknowledge
     *
     * @param channel
     * @param deliveryTag
     * @throws IOException
     */
    public void successAck(Channel channel, long deliveryTag) throws IOException {
        try {
            if (channel != null && channel.isOpen()) {
                channel.basicAck(deliveryTag, false);
                logger.infof(Utilities.getLogPreString("SMSApp")
                        + "successAck()"
                        + "|ACK [%s] tag:%d", "Success", deliveryTag);
            }
        } catch (IOException e) {
            throw e;
        }
    }

    /**
     *
     * @param channel
     * @param deliveryTag
     * @param requeue
     */
    public void rejectAck(Channel channel, long deliveryTag, boolean requeue) {
        try {
            if (channel != null && channel.isOpen()) {
                channel.basicReject(deliveryTag, requeue);
                logger.infof(Utilities.getLogPreString("SMSApp")
                        + "rejectAck()"
                        + "|ACK [%s] tag:%d", "Reject", deliveryTag);
            }
        } catch (IOException e) {
            logger.error(Utilities.getLogPreString("SMSApp")
                    + "rejectAck()"
                    + "IOException:" + e.getMessage()
                    + "|ACK Failed tag:" + deliveryTag);
        }
    }

    /**
     * handleMessage
     *
     * @param body
     * @param queueName
     * @param channel
     * @param deliveryTag
     */
    @SuppressWarnings("UseSpecificCatch")
    private void handleMessage(byte[] body, String queueName, Channel channel, long deliveryTag) {
        Instant tat = Instant.now();
        try {
            if (handleOutbox(body, queueName)) {
                successAck(channel, deliveryTag);
            } else {
                rejectAck(channel, deliveryTag, true);
            }

            logger.info(Utilities.getLogPreString("SMSApp")
                    + "handleMessage()"
                    + "-" + queueName
                    + "|Took " + Utilities.CalculateTAT(tat) + " mSec(s)"
                    + "|Tag:" + deliveryTag);
        } catch (Exception e) {
            rejectAck(channel, deliveryTag, true);

            logger.error(Utilities.getLogPreString("SMSApp")
                    + "handleMessage()"
                    + "-" + queueName
                    + "|Took " + Utilities.CalculateTAT(tat) + " mSec(s)"
                    + "|Exception:" + e.getMessage());
        }
    }

    /**
     * handleDownload
     *
     * @param body
     * @param queueName
     * @return
     */
    @SuppressWarnings("UseSpecificCatch")
    private boolean handleOutbox(byte[] body, String queueName) {

        boolean processed = false;
        String message = new String(body, StandardCharsets.UTF_8);
        try {
            logger.info(Utilities.getLogPreString("SMSApp")
                    + "handleOutbox()"
                    + "|Received"
                    + "-" + queueName
                    + "- Msg:" + message);

            SmsOutbox sms = new SmsOutbox();
            try {
                JSONObject obj = new JSONObject(message);
                sms.setSmsId(obj.getString("sms-id"));
                sms.setNetwork(obj.getString("network"));
                sms.setShortCode(obj.getString("short_code"));
                sms.setRecipients(obj.getString("recipients"));
                sms.setAuthToken(obj.getString("auth_token"));
                sms.setCampaignId(obj.getString("campaign_id"));
                sms.setOutboxId(obj.getString("outbox_id"));
                sms.setSmsPages(obj.getInt("message_pages"));
                sms.setMessage(obj.getString("message"));
                sms.setNetworkRegex(obj.getString("network_regex"));
                sms.setGateway(obj.getInt("gateway"));
                sms.setShortCode(obj.getString("short_code"));
                sms.setAlertType(obj.getString("alert_type"));
            } catch (Exception js) {
                logger.error(Utilities.getLogPreString("SMSApp")
                        + "handleOutbox()"
                        + "-" + queueName
                        + "|Exception:" + js.getMessage());
                return true;
            }

            Map<String, String> httpHeader = new HashMap<>();
            httpHeader.put("User-Agent", basics.UA);
            httpHeader.put("X-Organization-Id", "Mossbets/v1");

            String requestUrl = props.smsOutUrl();
            requestUrl = requestUrl.replace("{network_id}", sms.getNetworkRegex());

            Instant httpTime = Instant.now();
            Map<String, Object> httpResponse = ApiCalls.sendHttpJsonPostData(requestUrl,
                    new JSONObject()
                            .put("kmp_correlator", sms.getOutboxId())
                            .put("kmp_recipients", sms.getRecipients())
                            .put("kmp_code", sms.getShortCode())
                            .put("kmp_linkId", "")
                            .put("kmp_message", sms.getMessage())
                            .put("kmp_sms-id", sms.getSmsId())
                            .put("kmp_gateway", sms.getGateway())
                            .put("kmp_messageType", sms.getAlertType())
                            .put("kmp_network", sms.getNetwork())
                            .put("kmp_message_pages", sms.getSmsPages())
                            .toString(),
                    httpHeader,
                    basics,
                    logger, false);
            logger.info(Utilities.getLogPreString("ApiCalls")
                    + "handleOutbox()"
                    + "|Took " + Utilities.CalculateTAT(httpTime) + " mSec(s)"
                    + "|URL:" + requestUrl
                    + "|SendHttpJsonPostData()"
                    + "|Results =>" + httpResponse.toString());

            int httpCode = (int) httpResponse.get("statusCode");
            String resStr = httpResponse.get("response").toString();

            if (!Utilities.FindInIntegerArray(basics.succeessHttpStatuses, httpCode)
                    || Utilities.isBlank(resStr)) {
                return false;
            }

            return resStr.toLowerCase().startsWith("ok");
        } catch (Exception e) {
            logger.error(Utilities.getLogPreString("SMSApp")
                    + "handleOutbox()"
                    + "-" + queueName
                    + "|Exception:", e);
            processed = false;
        }

        return processed;
    }

    /**
     * cleanup
     */
    @PreDestroy
    @SuppressWarnings("UseSpecificCatch")
    void cleanup() {
        logger.info(Utilities.getLogPreString("SMSApp")
                + "cleanup()"
                + "|Shutting DOWN RabbitMQ Consumer gracefully...");
        running = false;
        try {
            if (connection != null && connection.isOpen()) {
                connection.close();
            }

            logger.info(Utilities.getLogPreString("SMSApp")
                    + "cleanup()"
                    + "|RabbitMQ Connection closed");
        } catch (Exception e) {
            logger.fatal(Utilities.getLogPreString("SMSApp")
                    + "cleanup()"
                    + "|Exception", e);
        }
    }

}

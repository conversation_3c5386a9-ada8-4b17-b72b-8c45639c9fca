package com.mossbets.app.utils;

import com.mossbets.app.utils.props.Basics;
import com.mossbets.app.utils.props.Props;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.inject.spi.CDI;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import javax.net.ssl.SSLContext;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.codehaus.jettison.json.JSONObject;
import org.jboss.logging.Logger;

@ApplicationScoped
public class ApiCalls {

    @SuppressWarnings("UseSpecificCatch")
    public static Map<String, Object> sendHttpJsonPostData(String postUrl, String payload,
            Map<String, String> postHeaders, final Basics config, final Logger logger,
            boolean hideResponse) {
        Instant startTime = Instant.now();
        Map<String, Object> resultMap = new HashMap<>();
        Props props = CDI.current().select(Props.class).get();

        postUrl = Utilities.CleanCallbackUrl(postUrl);
        if (!Utilities.ValidateUrl(postUrl)) {
            resultMap.put("statusCode", 400);
            resultMap.put("response", new JSONObject().toString());
            resultMap.put("error", "URISyntaxException<>Invalid URL!");
            resultMap.put("tat", Utilities.CalculateTAT(startTime));
            return resultMap;
        }

        if ((null == postHeaders) || (postHeaders.size() < 1)) {
            postHeaders = new HashMap<>();
        }

        postHeaders.put("Connection", "close");
        postHeaders.put("Accept", "*/*");

        if (!hideResponse) {
            logger.info(Utilities.getLogPreString("ApiCalls")
                    + "URL:" + postUrl
                    + "|(headers): " + postHeaders.toString()
                    + "|Request:" + payload);
        }

        int statusCode = 502;
        String responseContent = "";

        try {
            SSLContext sslContext = SSLContextBuilder.create()
                    .loadTrustMaterial(null, (chain, authType) -> true)
                    .build();

            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectTimeout(props.connectTimeOut())
                    .setSocketTimeout(props.socketTimeOut())
                    .setContentCompressionEnabled(true)
                    .setExpectContinueEnabled(true)
                    .build();

            try (CloseableHttpClient httpClient = HttpClients.custom()
                    .setSSLContext(sslContext)
                    .setDefaultRequestConfig(requestConfig)
                    .build();) {

                HttpPost postRequest = new HttpPost(postUrl);
                StringEntity input = new StringEntity(payload);
                input.setContentType("application/json");
                postRequest.setEntity(input);

                postHeaders.entrySet().forEach((Map.Entry<String, String> header) -> {
                    postRequest.addHeader(header.getKey().toLowerCase(), header.getValue());
                });

                try (CloseableHttpResponse response = httpClient.execute(postRequest)) {
                    statusCode = response.getStatusLine().getStatusCode();
                    responseContent = EntityUtils.toString(response.getEntity());

                    resultMap.put("statusCode", statusCode);
                    resultMap.put("response", responseContent);
                    resultMap.put("error", "");
                    resultMap.put("tat", Utilities.CalculateTAT(startTime));
                } catch (Exception e) {
                    throw new Exception("Exception<CallResponse>" + e.getMessage());
                }
            } catch (Exception e) {
                throw new Exception("Exception<CloseableHttpClient>" + e.getMessage());
            }
        } catch (Exception e) {
            resultMap.put("statusCode", statusCode);
            resultMap.put("response", responseContent);
            resultMap.put("error", "Http Error: " + e.getMessage());
            resultMap.put("tat", Utilities.CalculateTAT(startTime));
        }

        return resultMap;
    }

}

package com.mossbets.app.utils;

import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import jakarta.enterprise.context.ApplicationScoped;
import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.Instant;
import java.util.Arrays;
import java.util.Date;
import java.util.Formatter;
import java.util.HashMap;
import java.util.Map;
import java.util.TimeZone;
import java.util.concurrent.ThreadLocalRandom;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.apache.commons.lang3.StringUtils;
import org.codehaus.jettison.json.JSONArray;
import org.codehaus.jettison.json.JSONException;

@ApplicationScoped
public class Utilities {

    private static final String[] PREFIXES = {
        "11", "12", "13", "14",
        "71", "72", "74",
        "757", "758", "759",
        "768", "769",
        "79"
    };

    /**
     * Normalize a Kenyan MSISDN to +254… E.164, but only if the subscriber
     * prefix matches one of the allowed patterns.
     *
     * @param raw any string containing the number, e.g. "0743096273" or
     * "+254743096273"
     * @return the normalized E.164 MSISDN, e.g. "+254743096273"
     * @throws IllegalArgumentException if it can’t be parsed or has a
     * disallowed prefix
     */
    public static String formatKenyanMsisdn(String raw) {
        if (raw == null || raw.isBlank()) {
            throw new IllegalArgumentException("MSISDN must not be blank");
        }

        String digits = raw.replaceAll("\\D+", "");

        String subscriber;
        if (digits.startsWith("254") && digits.length() == 12) {
            subscriber = digits.substring(3);
        } else if (digits.startsWith("0") && digits.length() == 10) {
            subscriber = digits.substring(1);
        } else if (digits.length() == 9) {
            subscriber = digits;
        } else {
            throw new IllegalArgumentException(
                    "Cannot normalize MSISDN (unexpected length): " + raw);
        }

        if (!hasAllowedPrefix(subscriber)) {
            throw new IllegalArgumentException(
                    "Disallowed Kenyan prefix: " + subscriber);
        }

        return subscriber;
    }

    private static boolean hasAllowedPrefix(String subscriber) {
        for (String p : PREFIXES) {
            if (subscriber.startsWith(p)) {
                return true;
            }
        }
        return false;
    }

    /**
     * GetBFRAuthToken
     *
     * @return
     */
    public static String GetBFRAuthToken() {
        String keyBfr = "AblyAuth:AccessToken";
        String redisData = JedisUtils.selectOneData(keyBfr);
        if (Utilities.isBlank(redisData)) {
            redisData = "";
        }

        return redisData;
    }

    /**
     * SequentialMask
     *
     * @param inputStr
     * @param maskPercentage
     * @param maskChar
     * @return
     */
    public static String sequentialMask(String inputStr, double maskPercentage, char maskChar) {
        int length = inputStr.length();
        int maskCount = (int) Math.ceil((length - 2) * maskPercentage);
        StringBuilder masked = new StringBuilder(inputStr);

        for (int i = 1; i <= maskCount; i++) {
            masked.setCharAt(i, maskChar);
        }

        return masked.toString();
    }

    /**
     * GetLicense
     *
     * @return
     */
    public static String GetLicense() {
        String license = "[+]**********************************************[+]\n"
                + "[+]**********************************************[+]\n"
                + "[+]**********************************************[+]\n"
                + "[+]********          MossBets           *********[+]\n"
                + "[+]********                             *********[+]\n"
                + "[+]********          Liden.io           *********[+]\n"
                + "[+]********  AUTHOR: JOSEPHAT MUKUHA    *********[+]\n"
                + "[+]**********************************************[+]\n"
                + "[+]**********************************************[+]\n";

        return license;
    }

    /**
     * JsonValidator
     *
     * @param JsonStr
     * @param isArray
     * @return
     */
    public static boolean JsonValidator(String JsonStr, Boolean isArray) {

        if (isArray == true) {
            try {
                JSONArray jsonArray = new JSONArray(JsonStr);
                return true;
            } catch (JSONException jse) {
                return false;
            } catch (Exception e) {
                return false;
            }
        }

        Gson gson = new Gson();
        try {
            gson.fromJson(JsonStr, Object.class
            );
            Object jsonObjType = gson.fromJson(JsonStr, Object.class
            ).getClass();
            return !jsonObjType.equals(String.class
            );
        } catch (JsonSyntaxException ex) {
            return false;
        } catch (Exception ex) {
            return false;
        }
    }

    /**
     * CalculateWitholdingTax
     *
     * @param amountWon
     * @param stakeAmount
     * @param taxValue
     * @param taxationType
     * @return
     */
    public static Map<String, Double> CalculateWitholdingTax(Double amountWon,
            Double stakeAmount, Double taxValue, String taxationType) {

        Double possibleWin = (amountWon - stakeAmount);
        Double possibleWinAfter = (possibleWin - ((possibleWin * 100) / (100 + taxValue)));
        if (Utilities.containStr(new String[]{"DIRECT", "TYPE_1"}, taxationType.toUpperCase())) {
            possibleWinAfter = (possibleWin * (taxValue / 100));
        }

        Map<String, Double> whtax = new HashMap<>();
        whtax.put("posibleWin", (amountWon - possibleWinAfter));
        whtax.put("witholdingTax", possibleWinAfter);

        return whtax;
    }

    /**
     * CalculateExciseTax
     *
     * @param stakeAmount
     * @param taxValue
     * @param taxationType
     * @return
     */
    public static Map<String, Double> CalculateExciseTax(Double stakeAmount,
            Double taxValue, String taxationType) {
        Double stakeTax = stakeAmount - ((stakeAmount * 100) / (100 + taxValue));
        if (Utilities.containStr(new String[]{"DIRECT"}, taxationType.toUpperCase())) {
            stakeTax = (stakeAmount * (taxValue / 100));
        }

        Map<String, Double> exciseTax = new HashMap<>();
        exciseTax.put("stakeTax", stakeTax);
        exciseTax.put("stakeAfterTax", (stakeAmount - stakeTax));

        return exciseTax;
    }

    /**
     * containStr
     *
     * @param array
     * @param target
     * @return
     */
    public static boolean containStr(String[] array, String target) {
        if (array == null || target == null) {
            return false;
        }

        for (String value : array) {
            if (target.equalsIgnoreCase(value)) {
                return true;
            }
        }

        return false;
    }

    /**
     * getRandomValue
     *
     * @param array
     * @return
     */
    public static String getRandomValue(String[] array) {
        if (array == null || array.length == 0) {
            throw new IllegalArgumentException("The array must not be null or empty");
        }
        int randomIndex = ThreadLocalRandom.current().nextInt(array.length);
        return array[randomIndex];
    }

    /**
     * Round2Decimal
     *
     * @param input
     * @return
     */
    public static double Round2Decimal(double input) {
        return (Math.round(input * 100.00) / 100.00);
    }

    /**
     * RandomDecimal
     *
     * @param min
     * @param max
     * @param digit
     * @return
     */
    public static double RandomDecimal(double min, double max, int digit) {
        return ThreadLocalRandom.current().nextInt((int) (min * Math.pow(10, digit)), (int) (max * Math.pow(10, digit) + 1)) / Math.pow(10, digit);
    }

    /**
     * RandomDecimal
     *
     * @param min
     * @param max
     * @return
     */
    public static double RandomDecimal(double min, double max) {
        return RandomDecimal(min, max, 2);
    }

    /**
     *
     * @param array
     * @param y
     * @return
     */
    public static boolean FindInIntegerArray(int[] array, int y) {
        return Arrays.stream(array).anyMatch(value -> value == y);
    }

    /**
     * FindInStringArray
     *
     * @param array
     * @param cmp
     * @return
     */
    public static boolean FindInStringArray(String[] array, String cmp) {
        return Arrays.stream(array).anyMatch(value -> value.equals(cmp));
    }

    /**
     * GetRandomInt
     *
     * @param min
     * @param max
     * @return
     */
    public static int GetRandomInt(int min, int max) {
        return min + (int) (Math.random() * ((max - min) + 1));
    }

    /**
     * toSHA1
     *
     * @param password
     * @return
     */
    public static String toSHA1(String password) {
        String sha1 = "";
        try {
            MessageDigest crypt = MessageDigest.getInstance("SHA-1");
            crypt.reset();
            crypt.update(password.getBytes("UTF-8"));
            sha1 = byteToHex(crypt.digest());
        } catch (NoSuchAlgorithmException | UnsupportedEncodingException e) {
        }
        return sha1;
    }

    /**
     * byteToHex
     *
     * @param hash
     * @return
     */
    public static String byteToHex(final byte[] hash) {
        String result;
        try (Formatter formatter = new Formatter()) {
            for (byte b : hash) {
                formatter.format("%02x", b);
            }
            result = formatter.toString();
        }
        return result;
    }

    /**
     * CreateMd5
     *
     * @param input
     * @return
     * @throws NoSuchAlgorithmException
     */
    public static String CreateMd5(String input) throws NoSuchAlgorithmException {
        MessageDigest md = MessageDigest.getInstance("MD5");
        byte[] messageDigest = md.digest(input.getBytes());
        BigInteger no = new BigInteger(1, messageDigest);
        String hashtext = no.toString(16);
        while (hashtext.length() < 32) {
            hashtext = "0" + hashtext;
        }
        return hashtext;
    }

    public static long CalculateTAT(Instant start) {
        return Duration.between(start, Instant.now()).toMillis();
    }

    /**
     * CleanCallbackUrl
     *
     * @param Url
     * @return
     */
    public static String CleanCallbackUrl(String Url) {
        if (Url == null || Url.isEmpty()) {
            return Url; // Handle empty or null strings
        }

        int length = Url.length();
        return length > 1
                && Url.charAt(length - 1) == '/'
                ? Url.substring(0, length - 1)
                : Url;
    }

    /**
     * isBlank
     *
     * @param value
     * @return
     */
    @SuppressWarnings("null")
    public static boolean isBlank(String value) {
        if (null == value) {
            return true;
        }

        return ((null == value)
                || (StringUtils.isBlank(value))
                || (value.equals("null"))
                || (value.equals("false"))
                || (StringUtils.isEmpty(value))
                || (StringUtils.length(value) < 1));
    }

    /**
     * isNumeric
     *
     * @param strNum
     * @return
     */
    public static boolean isNumeric(String strNum) {
        try {
            double d = Double.parseDouble(strNum);
        } catch (NumberFormatException | NullPointerException nfe) {
            return false;
        }
        return true;
    }

    /**
     * ValidateUrl
     *
     * @param url
     * @return
     */
    public static boolean ValidateUrl(String url) {
        String urlRegex = "^(http|https)://[-a-zA-Z0-9+&@#/%?=~_|,!:.;]*[-a-zA-Z0-9+@#/%=&_|]";
        Pattern pattern = Pattern.compile(urlRegex);
        Matcher m = pattern.matcher(url);
        return m.matches();
    }

    /**
     *
     * @param format
     * @return
     */
    public static String now(String format) {
        return now(format, null);
    }

    /**
     *
     * @param format
     * @param timestamp
     * @return
     */
    public static String now(String format, Long timestamp) {
        if (timestamp == null) {
            timestamp = System.currentTimeMillis() / 1000; // Convert to seconds
        }

        SimpleDateFormat sdf = new SimpleDateFormat(format);
        sdf.setTimeZone(TimeZone.getTimeZone("Africa/Nairobi"));
        return sdf.format(new Date(timestamp * 1000)); // Convert to milliseconds
    }

    /**
     * getLogPreString
     *
     * @param appName
     * @return
     */
    public static String getLogPreString(String appName) {
        return appName + " | ";
    }

    /**
     * sha256
     *
     * @param input
     * @return
     */
    public static String sha256(String input) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hashBytes = digest.digest(input.getBytes());
            StringBuilder hexStr = new StringBuilder();
            for (byte b : hashBytes) {
                hexStr.append(String.format("%02x", b));
            }
            return hexStr.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-256 not supported", e);
        }
    }

}

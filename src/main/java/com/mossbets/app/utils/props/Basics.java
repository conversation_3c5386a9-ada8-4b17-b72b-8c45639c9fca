package com.mossbets.app.utils.props;

import jakarta.enterprise.context.ApplicationScoped;
import lombok.Data;

@Data
@ApplicationScoped
public class Basics {

    public String UA = "MPLY_APP_V2;Quarkus-3.12.3";
    public String HelpLine = "0111135736";
    public String SenderId = "MOSSBETS_TS";

    public int[] succeessHttpStatuses = new int[]{200, 201, 202};
    public int[] failedHttpCodes = {0, 404, 403, 405, 500, 502, 503, 504};
    public int[] finalBetStatus = {1, 3, 7, 9};
    public int[] allBetStatus = {0, 1, 3, 7, 9};
    public int[] finalBetRiskStatus = {1, 3};
    public int[] finalBetKRAStatus = {1};
    public int[] pendingWithdrawalStatus = {5000, 5001};

    //TransactionReferences 
    public int TRANSACTION_TYPE_CREDIT_ID = 1;
    public int TRANSACTION_TYPE_DEBIT_ID = 2;
    public int TRANSACTION_TYPE_REVERSAL_ID = 3;
    public int TRANSACTION_TYPE_VOIDED = 4;
    public int TRANSACTION_TYPE_TAXES = 5;
    public int TRANSACTION_TYPE_BONUS_BOOST = 6;
    public int TRANSACTION_TYPE_FREEBET_BOOST = 7;

    //Rand Vars
    public final int minRand = 0;
    public final int maxRand = 1;

    //Threading
    public final int BroadcastThreadPool = 10;

    //Redis Keys
    public String KRASettingKey = "KraComplianceSettings:{clientId}";
    public String KRATokenKey = "KraToken:{clientId}";

    public Basics() {

    }

}

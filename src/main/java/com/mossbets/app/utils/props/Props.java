package com.mossbets.app.utils.props;

import io.quarkus.arc.Unremovable;
import io.smallrye.config.ConfigMapping;
import io.smallrye.config.WithDefault;
import jakarta.enterprise.context.ApplicationScoped;

@ConfigMapping(prefix = "mbs")
@ApplicationScoped
@Unremovable
public interface Props {

    /**
     * Mail settings
     *
     * @return
     */
    @WithDefault("587")
    int mailPortNumber();

    String mailPassword();

    String mailUsername();

    String mailAdminAddress();

    String mailAdminHost();

    /**
     * Queue Configs
     *
     * @return
     */
    String[] WithdrawalQueues();//Withdrawal Qeue

    String[] SmsOutboxQueue();//SMS Queue

    String[] StkCheckoutQueues();//Checkout queue

    String[] RiskApprovalQueues();//Risk Approvals

    String[] DepositQueues();//Mepsa Deposits

    String[] KRAQueues();//KRA Queue{KRA_STAKE,KRA_RESULTS,KRA_PAYOUTS}

    String[] RefundQueues();//Refund Queues,LostBets Queues

    String[] BetSettlementQueues();//BetSettlement Queue

    String[] LiveBetQueues();//LiveBets Queues

    String[] depositFailedQueues();//Deposit Failed Queues

    String[] bonusQueues();//Bonus Queues

    /**
     * RabbitMQ Configs
     *
     * @return
     */
    @WithDefault("5672")

    int rabbitMqPort();

    @WithDefault("10000")
    int rabbitMqTimeout();

    @WithDefault("10000")
    int rabbitMqHandShakeTimeout();

    @WithDefault("45")
    int rabbitMqHeartbeat();

    @WithDefault("1000")
    int rabbitMqNetworkRecovery();

    @WithDefault("200")
    int rabbitMqPrefetchCount();

    @WithDefault("5000")
    int rabbitMqConnectionSleepTime();

    /**
     * RabbitMq Host
     *
     * @return
     */
    String rabbitMqHost();

    /**
     * RabbitMq user
     *
     * @return
     */
    String rabbitMqUsername();

    /**
     * RabbitMq Pass
     *
     * @return
     */
    String rabbitMqPassword();

    @WithDefault("MOSSBETS")
    String rabbitMqPrefix();

    @WithDefault("/")
    String rabbitMqVhost();

    @WithDefault("true")
    boolean rabbitMqConRecovery();

    @WithDefault("true")
    boolean rabbitMqTopologyRecovery();

    /**
     * Redis Configs
     *
     * @return
     */
    @WithDefault("6379")
    int redisPort();

    @WithDefault("2")
    int redisMinIdle();

    @WithDefault("2")
    int redisMaxIdle();

    @WithDefault("3")
    int redisMaxWait();

    @WithDefault("3540")
    int redisTimeOut();

    String redisHost();

    String redisUser();

    String redisAuth();

    @WithDefault("B2B")
    String redisStr();

    @WithDefault("100")
    int numberOfThreads();

    String smsOutUrl();

    double maxDeposit();

    double minDeposit();

    double riskMaxAmount();

    @WithDefault("20000")
    int connectTimeOut();

    @WithDefault("30000")
    int socketTimeOut();

    //URLS
    /**
     * Genius Risks URL
     *
     * @return
     */
    String geniusRiskUrl();

    String geniusRiskBfrUrl();

    String geniusXApiKey();

    /**
     * KRA URL
     *
     * @return
     */
    String kraPrnRegUrl();

    String kraPrnAuthUrl();

    String kraPrnGenUrl();

    String kraSafPayoutUrl();

    String kraStakeDataTransmitUrl();

    String kraStakeOutcomeTransmitUrl();

    /**
     * Mpesa Authorise
     *
     * @return
     */
    String mpesaSafAuthUrl();

    /**
     *
     * @return
     */
    String mpesaC2bBrokerKey();

    /**
     * Checkout
     *
     * @return
     */
    String mpesaSafCheckoutUrl();

    /**
     * withdrawalKey
     *
     * @return
     */
    String withdrawalKey();

    /**
     * Tax
     */
    double taxExcise();

    double taxWithholding();

}

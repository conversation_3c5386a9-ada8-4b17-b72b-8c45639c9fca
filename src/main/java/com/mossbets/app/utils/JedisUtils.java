package com.mossbets.app.utils;

import com.mossbets.app.utils.props.Props;
import jakarta.enterprise.context.Dependent;
import jakarta.enterprise.inject.spi.CDI;
import jakarta.inject.Inject;
import org.jboss.logging.Logger;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;
import redis.clients.jedis.exceptions.JedisException;

@Dependent
public class JedisUtils {

    @Inject
    Props props;

    private static String prefix;
    private static final Logger logger = Logger.getLogger(JedisUtils.class);

    /**
     * JedisConn
     *
     * @return
     */
    private static JedisPool JedisConn() {
        try {
            Props props = CDI.current().select(Props.class).get();
            prefix = props.rabbitMqPrefix();

            JedisPoolConfig jConfig = new JedisPoolConfig();
            jConfig.setMaxTotal(5);
            jConfig.setMaxIdle(props.redisMaxIdle());
            jConfig.setMinIdle(props.redisMinIdle());
            jConfig.setTestWhileIdle(true);
            jConfig.setTestOnBorrow(true);
            jConfig.setTestOnReturn(true);
            jConfig.setLifo(false);

            return new JedisPool(
                    jConfig,
                    props.redisHost(),
                    props.redisPort(),
                    props.redisTimeOut(),
                    props.redisAuth());
        } catch (Exception ex) {
            throw ex;
        }
    }

    /**
     * FetchAllData
     *
     * @param key
     * @return
     */
    @SuppressWarnings("null")
    public static String selectAllData(String key) {
        String dataObj = "";
        try {
            JedisPool jedisPool = JedisUtils.JedisConn();
            try (Jedis jedis = jedisPool.getResource();) {
                key = key.concat("*");
                dataObj = jedis.get(prefix.concat(key));
                jedisPool.close();
            } catch (JedisException ex) {
                if (null != jedisPool) {
                    jedisPool.close();
                }
            } finally {
                if (null != jedisPool) {
                    jedisPool.close();
                }
            }
        } catch (Exception ex) {
            throw ex;
        }

        return dataObj;
    }

    /**
     * FetchOneData
     *
     * @param key
     * @return
     */
    @SuppressWarnings("null")
    public static String selectOneData(String key) {
        String dataObj = "";
        try {
            JedisPool jedisPool = JedisUtils.JedisConn();
            try (Jedis jedis = jedisPool.getResource();) {
                dataObj = jedis.get(prefix.concat(key));
                jedisPool.close();

                if (null == dataObj) {
                    dataObj = "";
                }
            } catch (JedisException ex) {
                if (null != jedisPool) {
                    jedisPool.close();
                }
            } finally {
                if (null != jedisPool) {
                    jedisPool.close();
                }
            }
        } catch (Exception ex) {
            logger.error(Utilities.getLogPreString("JedisUtils")
                    + "JedisUtils.selectOneData()"
                    + "| Key:" + key
                    + "| Exception: " + ex.getMessage());
        }

        return dataObj;
    }

    /**
     * deleteData
     *
     * @param key
     * @return
     */
    @SuppressWarnings("null")
    public static boolean deleteData(String key) {
        boolean processed = false;
        try {
            JedisPool jedisPool = JedisUtils.JedisConn();
            try (Jedis jedis = jedisPool.getResource();) {
                jedis.del(prefix.concat(key));
                jedisPool.close();
                processed = true;
            } catch (JedisException ex) {
                if (null != jedisPool) {
                    jedisPool.close();
                }
            } finally {
                if (null != jedisPool) {
                    jedisPool.close();
                }
            }
        } catch (Exception ex) {
            logger.error(Utilities.getLogPreString("JedisUtils")
                    + "JedisUtils.deleteData()"
                    + "| Key:" + key
                    + "| Exception: " + ex.getMessage());
        }

        return processed;
    }

    /**
     * delAllData
     *
     * @param key
     * @return
     */
    @SuppressWarnings("null")
    public static boolean delAllData(String key) {
        boolean processed = false;
        try {
            JedisPool jedisPool = JedisUtils.JedisConn();
            try (Jedis jedis = jedisPool.getResource();) {
                key = key.concat("*");
                jedis.del(prefix.concat(key));
                jedisPool.close();
                processed = true;
            } catch (JedisException ex) {
                if (null != jedisPool) {
                    jedisPool.close();
                }
            } finally {
                if (null != jedisPool) {
                    jedisPool.close();
                }
            }
        } catch (Exception ex) {
            logger.error(Utilities.getLogPreString("JedisUtils")
                    + "JedisUtils.delAllData()"
                    + "| Key:" + key
                    + "| Exception: " + ex.getMessage());
        }

        return processed;
    }

    /**
     * saveData
     *
     * @param key
     * @param dataObj
     * @param timeout
     * @return
     */
    @SuppressWarnings("null")
    public static boolean saveData(String key, String dataObj, int timeout) {
        boolean processed = false;
        key = prefix.concat(key);
        try {
            JedisPool jedisPool = JedisUtils.JedisConn();
            try (Jedis jedis = jedisPool.getResource();) {
                jedis.del(key);

                if (timeout > 0) {
                    jedis.setex(key, timeout, dataObj);
                } else {
                    jedis.set(key, dataObj);
                }

                jedisPool.close();

                processed = true;
            } catch (JedisException ex) {
                if (null != jedisPool) {
                    jedisPool.close();
                }
            } finally {
                if (null != jedisPool) {
                    jedisPool.close();
                }
            }
        } catch (Exception ex) {
            logger.error(Utilities.getLogPreString("JedisUtils")
                    + "JedisUtils.saveData()"
                    + "| Key:" + key
                    + "| Exception: ", ex);
        }

        return processed;
    }
}

package com.mossbets.app.utils;

import com.mossbets.app.utils.props.Props;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.mail.*;
import jakarta.mail.internet.InternetAddress;
import jakarta.mail.internet.MimeBodyPart;
import jakarta.mail.internet.MimeMessage;
import jakarta.mail.internet.MimeMultipart;
import java.io.File;
import java.util.Properties;
import org.jboss.logging.Logger;

@ApplicationScoped
public class MailUtils {

    @Inject
    Props props;

    @Inject
    Logger logger;

    /**
     *
     * @param toEmail
     * @param name
     * @param emailBody
     * @param emailSubject
     * @param attachmentFile
     */
    public void SendMail(String toEmail, String name, String emailBody,
            String emailSubject, File attachmentFile) {

        try {
            String password = props.mailPassword();
            String username = props.mailUsername();
            String adminAddress = props.mailAdminAddress();

            Properties config = new Properties();
            config.put("mail.smtp.auth", true);
            config.put("mail.smtp.starttls.enable", true);
            config.put("mail.smtp.host", props.mailAdminHost());
            config.put("mail.smtp.port", props.mailPortNumber());

            Session session = Session.getInstance(config,
                    new jakarta.mail.Authenticator() {
                @Override
                protected PasswordAuthentication getPasswordAuthentication() {
                    return new PasswordAuthentication(username, password);
                }
            });

            Message message = new MimeMessage(session);
            message.setFrom(new InternetAddress(adminAddress));
            message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(toEmail));
            message.setSubject(emailSubject);

            MimeBodyPart htmlPart = new MimeBodyPart();
            htmlPart.setContent(generateEmailTemplate(name, emailBody,
                    emailSubject), "text/html; charset=utf-8");

            // Combine parts
            Multipart multipart = new MimeMultipart();
            multipart.addBodyPart(htmlPart);

            if (null != attachmentFile) {  // Attachment part
                MimeBodyPart attachmentPart = new MimeBodyPart();
                attachmentPart.attachFile(attachmentFile);
                multipart.addBodyPart(attachmentPart);
            }

            message.setContent(multipart);
            Transport.send(message);

            logger.info(Utilities.getLogPreString("SendMail")
                    + "Sent mail successfully....");
        } catch (Exception e) {
            logger.info(Utilities.getLogPreString("SendMail")
                    + "| Exception: " + e.getMessage()
                    + "Sent mail failed ....");
        }
    }

    /**
     * generateEmailTemplate
     *
     * @param recipientName
     * @param messageBody
     * @param subject
     * @return
     */
    public static String generateEmailTemplate(String recipientName, String messageBody, String subject) {
        return "<!DOCTYPE html>\n"
                + "<html>\n"
                + "<head>\n"
                + "  <meta charset=\"UTF-8\">\n"
                + "  <title>" + escapeHtml(subject) + "</title>\n"
                + "</head>\n"
                + "<body style=\"margin:0; padding:0; font-family: Arial, "
                + "sans-serif; background-color:#f4f4f4;\">\n"
                + "  <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" "
                + "style=\"background-color:#f4f4f4; padding:20px;\">\n"
                + "    <tr>\n"
                + "      <td>\n"
                + "        <table align=\"center\" width=\"600\" cellpadding=\"0\" "
                + "cellspacing=\"0\" style=\"background-color:#ffffff; border-radius:8px; "
                + "overflow:hidden; box-shadow:0 0 10px rgba(0,0,0,0.1);\">\n"
                + "          <tr>\n"
                + "            <td style=\"background-color:#4CAF50; padding:20px; "
                + "text-align:center; color:#ffffff; font-size:24px;\">\n"
                + "              " + escapeHtml(subject) + "\n"
                + "            </td>\n"
                + "          </tr>\n"
                + "          <tr>\n"
                + "            <td style=\"padding:30px; color:#333333; font-size:16px; line-height:1.5;\">\n"
                + "              <p>Hi " + escapeHtml(recipientName) + ",</p>\n"
                + "              <p>" + messageBody + "</p>\n"
                + "              <p>Best regards,<br>Bet fast, Bet mobile!</p>\n"
                + "            </td>\n"
                + "          </tr>\n"
                + "          <tr>\n"
                + "            <td style=\"background-color:#f4f4f4; padding:20px; "
                + "text-align:center; font-size:12px; color:#777;\">\n"
                + "              © " + java.time.Year.now() + " Kiron.Lite. All rights reserved.\n"
                + "            </td>\n"
                + "          </tr>\n"
                + "        </table>\n"
                + "      </td>\n"
                + "    </tr>\n"
                + "  </table>\n"
                + "</body>\n"
                + "</html>";
    }

    private static String escapeHtml(String text) {
        return text == null ? "" : text
                .replace("&", "&amp;")
                .replace("<", "&lt;")
                .replace(">", "&gt;")
                .replace("\"", "&quot;");
    }
}

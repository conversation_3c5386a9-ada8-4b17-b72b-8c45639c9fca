package com.mossbets.app.resources;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.inject.Produces;
import jakarta.inject.Named;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@ApplicationScoped
public class ExecutorPoolService {

    @Produces
    @ApplicationScoped
    @Named("queueExecutor")
    public ExecutorService queueExecutor() {
        int corePoolSize = 20;
        int maxPoolSize = 100;
        int queueSize = 1000;

        return new ThreadPoolExecutor(
                corePoolSize,
                maxPoolSize,
                60L,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(queueSize),
                Executors.defaultThreadFactory(),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }

    @Produces
    @ApplicationScoped
    @Named("refundExecutor")
    public ExecutorService refundExecutor() {
        int corePoolSize = 5;
        int maxPoolSize = 10;
        int queueSize = 50;

        return new ThreadPoolExecutor(
                corePoolSize,
                maxPoolSize,
                60L,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(queueSize),
                Executors.defaultThreadFactory(),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }

    @Produces
    @ApplicationScoped
    @Named("smsExecutor")
    public ExecutorService smsExecutor() {
        int corePoolSize = 20;
        int maxPoolSize = 50;
        int queueSize = 500;

        return new ThreadPoolExecutor(
                corePoolSize,
                maxPoolSize,
                60L,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(queueSize),
                Executors.defaultThreadFactory(),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }

    @Produces
    @ApplicationScoped
    @Named("stkExecutor")
    public ExecutorService stkExecutor() {
        int corePoolSize = 20;
        int maxPoolSize = 50;
        int queueSize = 500;

        return new ThreadPoolExecutor(
                corePoolSize,
                maxPoolSize,
                60L,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(queueSize),
                Executors.defaultThreadFactory(),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }

    @Produces
    @ApplicationScoped
    @Named("walletExecutor")
    public ExecutorService walletExecutor() {
        int corePoolSize = 20;
        int maxPoolSize = 50;
        int queueSize = 500;

        return new ThreadPoolExecutor(
                corePoolSize,
                maxPoolSize,
                60L,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(queueSize),
                Executors.defaultThreadFactory(),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }

    @Produces
    @ApplicationScoped
    @Named("kraExecutor")
    public ExecutorService kraExecutor() {
        int corePoolSize = 10;
        int maxPoolSize = 20;
        int queueSize = 50;

        return new ThreadPoolExecutor(
                corePoolSize,
                maxPoolSize,
                60L,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(queueSize),
                Executors.defaultThreadFactory(),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }
}

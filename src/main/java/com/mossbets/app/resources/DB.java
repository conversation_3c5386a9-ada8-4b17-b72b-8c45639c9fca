package com.mossbets.app.resources;

import io.agroal.api.AgroalDataSource;
import io.quarkus.agroal.DataSource;
import io.quarkus.runtime.Startup;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

@Startup
@ApplicationScoped
public class DB {

    @Inject
    @DataSource("db1")
    private AgroalDataSource dbWrProfile;

    @Inject
    @DataSource("db2")
    private AgroalDataSource dbWrBonus;

    @Inject
    @DataSource("db3")
    private AgroalDataSource dbWrTrxn;

    @Inject
    @DataSource("db4")
    private AgroalDataSource dbWrBets;

    /**
     * Read Datasources
     */
    @Inject
    @DataSource("dbr1")
    private AgroalDataSource dbrProfile;

    @Inject
    @DataSource("dbr2")
    private AgroalDataSource dbrBonus;

    @Inject
    @DataSource("dbr3")
    private AgroalDataSource dbrTrxn;

    @Inject
    @DataSource("dbr4")
    private AgroalDataSource dbrWBets;

    @Inject
    @DataSource("dbr5")
    private AgroalDataSource dbrUser;

    /**
     * WriteDataSource
     *
     * @param db
     * @return
     */
    public AgroalDataSource WriteDataSource(String db) {
        return switch (db.toLowerCase()) {
            case "profile" ->
                dbWrProfile;
            case "bonus" ->
                dbWrBonus;
            case "trxn" ->
                dbWrTrxn;
            case "bets" ->
                dbWrBets;
            default ->
                dbWrTrxn;
        };
    }

    /**
     * readDataSource
     *
     * @param db
     * @return
     */
    public AgroalDataSource ReadDataSource(String db) {
        return switch (db.toLowerCase()) {
            case "user" ->
                dbrUser;
            case "profile" ->
                dbrProfile;
            case "bonus" ->
                dbrBonus;
            case "trxn" ->
                dbrTrxn;
            case "bets" ->
                dbrWBets;
            default ->
                dbrTrxn;
        };
    }
}

package com.mossbets.app.rest.api;

import com.mossbets.app.rest.api.response.ApiHttpResponse;
import com.mossbets.app.utils.Queue;
import com.mossbets.app.utils.Utilities;
import com.mossbets.app.utils.props.Basics;
import jakarta.enterprise.context.RequestScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.NotFoundException;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.HttpHeaders;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.time.Instant;
import org.codehaus.jettison.json.JSONException;
import org.codehaus.jettison.json.JSONObject;
import org.jboss.logging.Logger;
import org.jboss.resteasy.reactive.Cache;

@Path("/sms/v1/")
@RequestScoped
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
public class SmsOndemandResource {

    @Inject
    public Logger logger;

    @Inject
    public Basics basics = new Basics();

    @Inject
    private Queue Queue;

    @POST
    @Cache
    @Path("inbox")
    @SuppressWarnings("UseSpecificCatch")
    public Response Inbox(@PathParam("transactionId") final String transactionId,
            @Context HttpHeaders headers, String request) throws JSONException, NotFoundException {
        Instant startTime = Instant.now();
        JSONObject result = new JSONObject();

        try {
            logger.info(Utilities.getLogPreString("MpesaCheckout")
                    + "TrxnId:" + transactionId
                    + "|Request: " + request);

            result.put("code", "Success");
            result.put("statusDescription", "Request is successful");
            result.put("data", new JSONObject()
                    .put("code", 200)
                    .put("message", "Success"));
            return ApiHttpResponse.Send(result, 200, startTime, true);
        } catch (Exception jse) {
            logger.error(Utilities.getLogPreString("DsResource")
                    + "| Exception: ", jse);

            result.put("code", "Error");
            result.put("statusDescription", "Request is not successful");
            result.put("data", new JSONObject()
                    .put("code", 500)
                    .put("message", "Internal Server Error."));
            return ApiHttpResponse.Send(result, 200, startTime, true);
        }
    }
}

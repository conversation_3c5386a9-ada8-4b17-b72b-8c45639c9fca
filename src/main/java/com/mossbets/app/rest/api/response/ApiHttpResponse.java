package com.mossbets.app.rest.api.response;

import com.mossbets.app.utils.Utilities;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.ws.rs.core.Response;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import org.codehaus.jettison.json.JSONObject;
import org.jboss.logging.Logger;

@ApplicationScoped
public class ApiHttpResponse {

    /**
     * Response
     *
     * Send CORS(Do not alter any parameters here unless instructed to)
     *
     * @param result
     * @param statusCode
     * @param startTime
     * @param logToFile
     * @return
     */
    public static Response Send(JSONObject result, int statusCode, Instant startTime, Boolean logToFile) {
        if (logToFile) {
            Logger.getLogger(ApiHttpResponse.class).info(Utilities.getLogPreString("Response")
                    + HttpReason(statusCode)
                    + "| Took " + Utilities.CalculateTAT(startTime) + " mSec(s)"
                    + "| " + result.toString());
        }

        return Response.status(statusCode, HttpReason(statusCode))
                .header("Status", statusCode + " " + HttpReason(statusCode).toUpperCase())
                .header("Accept-Encoding", "gzip, deflate, br")
                .header("Content-Type", "application/json")
                .header("Accept-Charset", "utf-8")
                .header("Access-Control-Allow-Headers", "Access-Control-Allow-Headers"
                        + ",Origin,Accept,X-Requested-With,Content-Type,Authorization"
                        + ",Access-Control-Request-Method,Access-Control-Request-Headers"
                        + ",x-hash-key,x-requested-with,x-signature,x-api-key,x-access,x-app-key")
                .header("Access-Control-Expose-Headers", "Content-Length,X-JSON")
                .header("x-tat", Utilities.CalculateTAT(startTime) + " mSec(s)")
                .entity(result.toString())
                .build();
    }

    /**
     * HttpReason
     *
     * @param code
     * @return
     */
    private static String HttpReason(int code) {
        Map<Integer, String> arr = new HashMap<>();
        arr.put(200, "OK");
        arr.put(201, "CREATED");
        arr.put(202, "ACCEPTED");
        arr.put(204, "NO CONTENT");
        arr.put(304, "NOT MODIFIED");
        arr.put(400, "BAD REQUEST");
        arr.put(401, "UNAUTHORIZED REQUEST");
        arr.put(402, "PAYMENT REQUIRED");
        arr.put(403, "REQUEST FORBIDDEN");
        arr.put(404, "NOT FOUND");
        arr.put(408, "REQUEST TIMED-OUT");
        arr.put(405, "METHOD NOT ALLOWED");
        arr.put(421, "MISDIRECTED REQUEST");
        arr.put(422, "UNPROCESSABLE ENTITY");
        arr.put(429, "Too Many Requests");
        arr.put(500, "INTERNAL SERVER ERROR");
        arr.put(502, "BAD GATEWAY");
        arr.put(503, "Service Unavailable");
        arr.put(504, "Gateway Timeout");

        return arr.getOrDefault(code, "");
    }
}

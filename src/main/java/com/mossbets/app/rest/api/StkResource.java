package com.mossbets.app.rest.api;

import com.mossbets.app.resources.DB;
import com.mossbets.app.rest.api.response.ApiHttpResponse;
import com.mossbets.app.utils.JedisUtils;
import com.mossbets.app.utils.Queue;
import com.mossbets.app.utils.Utilities;
import com.mossbets.app.utils.props.Basics;
import jakarta.enterprise.context.RequestScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.NotFoundException;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.HttpHeaders;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import org.codehaus.jettison.json.JSONArray;
import org.codehaus.jettison.json.JSONException;
import org.codehaus.jettison.json.JSONObject;
import org.jboss.logging.Logger;
import org.jboss.resteasy.reactive.Cache;

@Path("/cb/v1/")
@RequestScoped
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
public class StkResource {

    @Inject
    public Logger logger;

    @Inject
    public Basics basics = new Basics();

    @Inject
    private DB db;

    @Inject
    private Queue Queue;

    @POST
    @Cache
    @Path("in/{transactionId}")
    @SuppressWarnings("UseSpecificCatch")
    public Response MpesaCheckout(@PathParam("transactionId") final String transactionId,
            @Context HttpHeaders headers, String request) throws JSONException, NotFoundException {
        Instant startTime = Instant.now();
        JSONObject result = new JSONObject();
        try {
            logger.info(Utilities.getLogPreString("MpesaCheckout")
                    + "TrxnId:" + transactionId
                    + "|Request: " + request);

            String MerchantRequestID = "";
            String CheckoutRequestID = "";
            JSONArray items;
            try {
                JSONObject reqObj = new JSONObject(request);
                JSONObject stkBody = reqObj.getJSONObject("Body");
                JSONObject stk = stkBody.getJSONObject("stkCallback");

                if (stk.getInt("ResultCode") != 0) {
                    result.put("code", "Error");
                    result.put("statusDescription", "Request is not successful");
                    result.put("data", new JSONObject()
                            .put("code", 422)
                            .put("message", stk.getString("ResultDesc")));
                    return ApiHttpResponse.Send(result, 200, startTime, true);
                }

                MerchantRequestID = stk.getString("MerchantRequestID");
                CheckoutRequestID = stk.getString("CheckoutRequestID");
                items = stk.getJSONObject("CallbackMetadata").getJSONArray("Item");
            } catch (Exception jse) {
                jse.printStackTrace();
                result.put("code", "Error");
                result.put("statusDescription", "Request is not successful");
                result.put("data", new JSONObject()
                        .put("code", 422)
                        .put("message", "Bad request!"));
                return ApiHttpResponse.Send(result, 200, startTime, true);
            }

            if (Utilities.isBlank(transactionId)) {
                result.put("code", "Error");
                result.put("statusDescription", "Request is not successful");
                result.put("data", new JSONObject()
                        .put("code", 422)
                        .put("message", "Mandatory field(s) required!"));
                return ApiHttpResponse.Send(result, 200, startTime, true);
            }

            Map<String, Object> profile = null;

            String profileKey = "StkProfile:TrxnId:" + transactionId;
            String rStr = JedisUtils.selectOneData(profileKey);
            if (!Utilities.isBlank(rStr)) {
                try {
                    profile = new JSONObject(rStr).toMap();
                } catch (JSONException s) {
                    profile = null;
                }
            }

            if (null == profile) {
                try (final java.sql.Connection dbConn = db.ReadDataSource("profile").getConnection();
                        final PreparedStatement ps = dbConn.prepareStatement(
                                "SELECT p.id profile_id,p.msisdn,p.name,p.hash,p.network "
                                + "FROM profile p WHERE p.status=1 AND p.acc_number=?");) {
                    ps.setString(1, transactionId);
                    try (ResultSet rs = ps.executeQuery()) {
                        while (rs.next()) {
                            profile = new HashMap<>();
                            profile.put("pId", rs.getLong("profile_id"));
                            profile.put("msisdn", rs.getLong("msisdn"));
                            profile.put("hash", rs.getString("hash"));
                            profile.put("network", rs.getString("network"));
                            profile.put("name", "");

                            if (!Utilities.isBlank(rs.getString("name"))) {
                                profile.put("name", rs.getString("name"));
                            }
                        }
                    } catch (SQLException e) {
                        throw e;
                    }
                } catch (Exception e) {
                }
            }

            if (null == profile) {
                result.put("code", "Error");
                result.put("statusDescription", "Request is not successful");
                result.put("data", new JSONObject()
                        .put("code", 400)
                        .put("message", "Invalid profile!"));
                return ApiHttpResponse.Send(result, 200, startTime, true);
            }

            double amount = 0.0;
            double balance = 0.0;
            long PhoneNumber = 0;
            long TransactionDate = 0;
            String recieptNo = "";

            for (int i = 0; i < items.length(); i++) {
                JSONObject item = items.getJSONObject(i);

                String name = item.getString("Name");
                if (name.equalsIgnoreCase("Amount")) {
                    amount = item.getDouble("Value");
                    continue;
                }

                if (name.equalsIgnoreCase("MpesaReceiptNumber")) {
                    recieptNo = item.getString("Value");
                    continue;
                }

                if (name.equalsIgnoreCase("PhoneNumber")) {
                    PhoneNumber = item.getLong("Value");
                    continue;
                }

                if (name.equalsIgnoreCase("TransactionDate")) {
                    TransactionDate = item.getLong("Value");
                }

                if (name.equalsIgnoreCase("Balance")) {
                    //{"Name":"Balance","Value":"{Amount={CurrencyCode=KES, 
                    //MinimumAmount=9840100, BasicAmount=98401.00}}"}
                    try {
                        JSONObject balanceJson = new JSONObject(
                                item.getString("Value")
                                        .replaceAll("=(\\w+)", "=\"$1\""));
                        balance = balanceJson.getJSONObject("Amount")
                                .getDouble("BasicAmount");
                    } catch (Exception e) {

                    }
                }
            }

            if (amount < 0) {
                result.put("code", "Error");
                result.put("statusDescription", "Request is not successful");
                result.put("data", new JSONObject()
                        .put("code", 400)
                        .put("message", "Invalid Amount!"));
                return ApiHttpResponse.Send(result, 200, startTime, true);
            }

            if ((PhoneNumber < 0) || (PhoneNumber != (long) profile.get("msisdn"))) {
                result.put("code", "Error");
                result.put("statusDescription", "Request is not successful");
                result.put("data", new JSONObject()
                        .put("code", 400)
                        .put("message", "Invalid PhoneNumber.." + profile.get("msisdn")));
                return ApiHttpResponse.Send(result, 200, startTime, true);
            }

            if (Utilities.isBlank(recieptNo)) {
                result.put("code", "Error");
                result.put("statusDescription", "Request is not successful");
                result.put("data", new JSONObject()
                        .put("code", 400)
                        .put("message", "Invalid  MpesaReceiptNumber!"));
                return ApiHttpResponse.Send(result, 200, startTime, true);
            }

            String[] parts = profile.get("name").toString().split("\\s+");
            String firstName = parts.length > 0 ? parts[0] : "";
            String middleName = parts.length > 2 ? parts[1] : "";
            String lastName = parts.length > 2 ? parts[2] : parts.length > 1 ? parts[1] : "";

            String sign = PhoneNumber + "" + recieptNo + "" + amount + "" + CheckoutRequestID;

            String queue = "DEPOSIT_C2B";
            int rand = Utilities.GetRandomInt(0, 2);
            if (rand > 0) {
                queue = queue.concat("_" + rand);
            }

            if (!Queue.publishMessage(new JSONObject()
                    .put("MSISDN", PhoneNumber)
                    .put("TransactionType", "Customer Payment Via OD")
                    .put("BillRefNumber", transactionId)
                    .put("TransID", recieptNo)
                    .put("FirstName", firstName)
                    .put("MiddleName", middleName)
                    .put("LastName", lastName)
                    .put("BusinessShortCode", "736736")
                    .put("OrgAccountBalance", balance)
                    .put("TransAmount", amount)
                    .put("TransTime", Utilities.now("yyyy-MM-dd HH:mm:ss"))
                    .put("ThirdPartyTransID", CheckoutRequestID)
                    .put("MerchantRequestID", MerchantRequestID)
                    .put("Signature", Utilities.CreateMd5(sign))
                    .toString(),
                    queue, queue, queue,
                    null, null)) {
                result.put("code", "Error");
                result.put("statusDescription", "Request is not successful");
                result.put("data", new JSONObject()
                        .put("code", 400)
                        .put("message", "Publising failed:" + recieptNo));
                return ApiHttpResponse.Send(result, 200, startTime, true);
            }

            result.put("code", "Success");
            result.put("statusDescription", "Request is successful");
            result.put("data", new JSONObject()
                    .put("code", 200)
                    .put("message", "Success"));
            return ApiHttpResponse.Send(result, 200, startTime, true);
        } catch (Exception jse) {
            logger.error(Utilities.getLogPreString("DsResource")
                    + "TransactionId:" + transactionId
                    + "| Exception: ", jse);

            result.put("code", "Error");
            result.put("statusDescription", "Request is not successful");
            result.put("data", new JSONObject()
                    .put("code", 500)
                    .put("message", "Internal Server Error."));
            return ApiHttpResponse.Send(result, 200, startTime, true);
        }
    }

}

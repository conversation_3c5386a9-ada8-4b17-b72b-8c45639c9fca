#Production datasource

#Profile
quarkus.datasource."db1".db-kind=mysql
quarkus.datasource."db1".username=${DB_USERNAME}
quarkus.datasource."db1".password=${DB_PASSWORD}
quarkus.datasource."db1".jdbc.initial-size=5
quarkus.datasource."db1".jdbc.max-size=15
quarkus.datasource."db1".jdbc.driver=com.mysql.cj.jdbc.Driver
quarkus.datasource."db1".jdbc.url=jdbc:mysql://${DB_WRITE}:3306/mossbets_profile?useSSL=false&autoreconnect=true&useUnicode=true&characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull&relaxAutoCommit=true&useServerPrepStmts=false&rewriteBatchedStatements=true&character_set_server=utf8mb4&allowPublicKeyRetrieval=true
quarkus.datasource."db1".jdbc.validation-query-sql=select 1
quarkus.datasource."db1".jdbc.pooling-enabled=true
quarkus.datasource."db1".jdbc.min-size=1
quarkus.datasource."db1".jdbc.idle-removal-interval=60S
quarkus.datasource."db1".jdbc.background-validation-interval=10
quarkus.datasource."db1".jdbc.leak-detection-interval=30
quarkus.datasource."db1".jdbc.acquisition-timeout=10
quarkus.datasource."db1".jdbc.max-lifetime=1800S

#Bonus DB
quarkus.datasource."db2".db-kind=mysql
quarkus.datasource."db2".username=${DB_USERNAME}
quarkus.datasource."db2".password=${DB_PASSWORD}
quarkus.datasource."db2".jdbc.initial-size=5
quarkus.datasource."db2".jdbc.max-size=15
quarkus.datasource."db2".jdbc.driver=com.mysql.cj.jdbc.Driver
quarkus.datasource."db2".jdbc.url=jdbc:mysql://${DB_WRITE}:3306/mossbets_bonus?useSSL=false&autoreconnect=true&useUnicode=true&characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull&relaxAutoCommit=true&useServerPrepStmts=false&rewriteBatchedStatements=true&character_set_server=utf8mb4&allowPublicKeyRetrieval=true
quarkus.datasource."db2".jdbc.validation-query-sql=select 1
quarkus.datasource."db2".jdbc.pooling-enabled=true
quarkus.datasource."db2".jdbc.min-size=1
quarkus.datasource."db2".jdbc.idle-removal-interval=60S
quarkus.datasource."db2".jdbc.background-validation-interval=10
quarkus.datasource."db2".jdbc.leak-detection-interval=30
quarkus.datasource."db2".jdbc.acquisition-timeout=10
quarkus.datasource."db2".jdbc.max-lifetime=1800S

#Transactions DB
quarkus.datasource."db3".db-kind=mysql
quarkus.datasource."db3".username=${DB_USERNAME}
quarkus.datasource."db3".password=${DB_PASSWORD}
quarkus.datasource."db3".jdbc.initial-size=5
quarkus.datasource."db3".jdbc.max-size=15
quarkus.datasource."db3".jdbc.driver=com.mysql.cj.jdbc.Driver
quarkus.datasource."db3".jdbc.url=jdbc:mysql://${DB_WRITE}:3306/mossbets_transactions?useSSL=false&autoreconnect=true&useUnicode=true&characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull&relaxAutoCommit=true&useServerPrepStmts=false&rewriteBatchedStatements=true&character_set_server=utf8mb4&allowPublicKeyRetrieval=true
quarkus.datasource."db3".jdbc.validation-query-sql=select 1
quarkus.datasource."db3".jdbc.pooling-enabled=true
quarkus.datasource."db3".jdbc.min-size=1
quarkus.datasource."db3".jdbc.idle-removal-interval=60S
quarkus.datasource."db3".jdbc.background-validation-interval=10
quarkus.datasource."db3".jdbc.leak-detection-interval=30
quarkus.datasource."db3".jdbc.acquisition-timeout=10
quarkus.datasource."db3".jdbc.max-lifetime=1800S

#Bets DB
quarkus.datasource."db4".db-kind=mysql
quarkus.datasource."db4".username=${DB_USERNAME}
quarkus.datasource."db4".password=${DB_PASSWORD}
quarkus.datasource."db4".jdbc.initial-size=5
quarkus.datasource."db4".jdbc.max-size=20
quarkus.datasource."db4".jdbc.driver=com.mysql.cj.jdbc.Driver
quarkus.datasource."db4".jdbc.url=jdbc:mysql://${DB_WRITE}:3306/mossbets_bets?useSSL=false&autoreconnect=true&useUnicode=true&characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull&relaxAutoCommit=true&useServerPrepStmts=false&rewriteBatchedStatements=true&character_set_server=utf8mb4&allowPublicKeyRetrieval=true
quarkus.datasource."db4".jdbc.validation-query-sql=select 1
quarkus.datasource."db4".jdbc.pooling-enabled=true
quarkus.datasource."db4".jdbc.min-size=1
quarkus.datasource."db4".jdbc.idle-removal-interval=60S
quarkus.datasource."db4".jdbc.background-validation-interval=10
quarkus.datasource."db4".jdbc.leak-detection-interval=30
quarkus.datasource."db4".jdbc.acquisition-timeout=10
quarkus.datasource."db4".jdbc.max-lifetime=1800S

##
## Slave DB
##
#Profile Read DB
quarkus.datasource."dbr1".db-kind=mysql
quarkus.datasource."dbr1".username=${DB_USERNAME}
quarkus.datasource."dbr1".password=${DB_PASSWORD}
quarkus.datasource."dbr1".jdbc.initial-size=5
quarkus.datasource."dbr1".jdbc.max-size=15
quarkus.datasource."dbr1".jdbc.driver=com.mysql.cj.jdbc.Driver
quarkus.datasource."dbr1".jdbc.url=jdbc:mysql://${DB_READ}:3306/mossbets_profile?useSSL=false&autoreconnect=true&useUnicode=true&characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull&useServerPrepStmts=false&rewriteBatchedStatements=true&character_set_server=utf8mb4&allowPublicKeyRetrieval=true
quarkus.datasource."dbr1".jdbc.validation-query-sql=select 1
quarkus.datasource."dbr1".jdbc.pooling-enabled=true
quarkus.datasource."dbr1".jdbc.min-size=1
quarkus.datasource."dbr1".jdbc.idle-removal-interval=60S
quarkus.datasource."dbr1".jdbc.background-validation-interval=10
quarkus.datasource."dbr1".jdbc.leak-detection-interval=30
quarkus.datasource."dbr1".jdbc.acquisition-timeout=10
quarkus.datasource."dbr1".jdbc.max-lifetime=1800S

#Bonus Read DB
quarkus.datasource."dbr2".db-kind=mysql
quarkus.datasource."dbr2".username=${DB_USERNAME}
quarkus.datasource."dbr2".password=${DB_PASSWORD}
quarkus.datasource."dbr2".jdbc.initial-size=5
quarkus.datasource."dbr2".jdbc.max-size=15
quarkus.datasource."dbr2".jdbc.driver=com.mysql.cj.jdbc.Driver
quarkus.datasource."dbr2".jdbc.url=jdbc:mysql://${DB_READ}:3306/mossbets_bonus?useSSL=false&autoreconnect=true&useUnicode=true&characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull&useServerPrepStmts=false&rewriteBatchedStatements=true&character_set_server=utf8mb4&allowPublicKeyRetrieval=true
quarkus.datasource."dbr2".jdbc.validation-query-sql=select 1
quarkus.datasource."dbr2".jdbc.pooling-enabled=true
quarkus.datasource."dbr2".jdbc.min-size=1
quarkus.datasource."dbr2".jdbc.idle-removal-interval=60S
quarkus.datasource."dbr2".jdbc.background-validation-interval=10
quarkus.datasource."dbr2".jdbc.leak-detection-interval=30
quarkus.datasource."dbr2".jdbc.acquisition-timeout=10
quarkus.datasource."dbr2".jdbc.max-lifetime=1800S

#Transactions Read DB
quarkus.datasource."dbr3".db-kind=mysql
quarkus.datasource."dbr3".username=${DB_USERNAME}
quarkus.datasource."dbr3".password=${DB_PASSWORD}
quarkus.datasource."dbr3".jdbc.initial-size=5
quarkus.datasource."dbr3".jdbc.max-size=15
quarkus.datasource."dbr3".jdbc.driver=com.mysql.cj.jdbc.Driver
quarkus.datasource."dbr3".jdbc.url=jdbc:mysql://${DB_READ}:3306/mossbets_transactions?useSSL=false&autoreconnect=true&useUnicode=true&characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull&useServerPrepStmts=false&rewriteBatchedStatements=true&character_set_server=utf8mb4&allowPublicKeyRetrieval=true
quarkus.datasource."dbr3".jdbc.validation-query-sql=select 1
quarkus.datasource."dbr3".jdbc.pooling-enabled=true
quarkus.datasource."dbr3".jdbc.min-size=1
quarkus.datasource."dbr3".jdbc.idle-removal-interval=60S
quarkus.datasource."dbr3".jdbc.background-validation-interval=10
quarkus.datasource."dbr3".jdbc.leak-detection-interval=30
quarkus.datasource."dbr3".jdbc.acquisition-timeout=10
quarkus.datasource."dbr3".jdbc.max-lifetime=1800S

#Bets Read DB
quarkus.datasource."dbr4".db-kind=mysql
quarkus.datasource."dbr4".username=${DB_USERNAME}
quarkus.datasource."dbr4".password=${DB_PASSWORD}
quarkus.datasource."dbr4".jdbc.initial-size=5
quarkus.datasource."dbr4".jdbc.max-size=15
quarkus.datasource."dbr4".jdbc.driver=com.mysql.cj.jdbc.Driver
quarkus.datasource."dbr4".jdbc.url=jdbc:mysql://${DB_READ}:3306/mossbets_bets?useSSL=false&autoreconnect=true&useUnicode=true&characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull&useServerPrepStmts=false&rewriteBatchedStatements=true&character_set_server=utf8mb4&allowPublicKeyRetrieval=true
quarkus.datasource."dbr4".jdbc.validation-query-sql=select 1
quarkus.datasource."dbr4".jdbc.pooling-enabled=true
quarkus.datasource."dbr4".jdbc.min-size=1
quarkus.datasource."dbr4".jdbc.idle-removal-interval=60S
quarkus.datasource."dbr4".jdbc.background-validation-interval=10
quarkus.datasource."dbr4".jdbc.leak-detection-interval=30
quarkus.datasource."dbr4".jdbc.acquisition-timeout=10
quarkus.datasource."dbr4".jdbc.max-lifetime=1800S

#User
quarkus.datasource."dbr5".db-kind=mysql
quarkus.datasource."dbr5".username=${DB_USERNAME}
quarkus.datasource."dbr5".password=${DB_PASSWORD}
quarkus.datasource."dbr5".jdbc.initial-size=5
quarkus.datasource."dbr5".jdbc.max-size=5
quarkus.datasource."dbr5".jdbc.driver=com.mysql.cj.jdbc.Driver
quarkus.datasource."dbr5".jdbc.url=jdbc:mysql://${DB_READ}:3306/mossbets_user?useSSL=false&autoreconnect=true&useUnicode=true&characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull&useServerPrepStmts=false&rewriteBatchedStatements=true&character_set_server=utf8mb4&allowPublicKeyRetrieval=true
quarkus.datasource."dbr5".jdbc.validation-query-sql=select 1
quarkus.datasource."dbr5".jdbc.pooling-enabled=true
quarkus.datasource."dbr5".jdbc.min-size=1
quarkus.datasource."dbr5".jdbc.idle-removal-interval=60S
quarkus.datasource."dbr5".jdbc.background-validation-interval=10
quarkus.datasource."dbr5".jdbc.leak-detection-interval=30
quarkus.datasource."dbr5".jdbc.acquisition-timeout=10
quarkus.datasource."dbr5".jdbc.max-lifetime=1800S


#Activate(Write) Datasources
%db1.quarkus.datasource."db1".active=true
%db2.quarkus.datasource."db2".active=true
%db3.quarkus.datasource."db3".active=true
%db4.quarkus.datasource."db4".active=true

#Activate(Read) Datasources
%dbr1.quarkus.datasource."dbr1".active=true
%dbr2.quarkus.datasource."dbr2".active=true
%dbr3.quarkus.datasource."dbr3".active=true
%dbr4.quarkus.datasource."dbr4".active=true
%dbr5.quarkus.datasource."dbr5".active=true


quarkus.datasource.devservices.enabled=false

#logging
quarkus.log.file.level=ALL
quarkus.log.file.enable=true
quarkus.log.console.enable=false
quarkus.log.file.path=/var/log/java/mossbets.log
quarkus.log.file.format=%d{yyyy-MM-dd HH:mm:ss,SSS} : %-5p : %m%n
quarkus.log.file.rotation.max-file-size=5000M
quarkus.log.file.rotation.max-backup-index=10
quarkus.log.file.rotation.file-suffix=.yyyy-MM-dd
quarkus.log.file.rotation.rotate-on-boot=true

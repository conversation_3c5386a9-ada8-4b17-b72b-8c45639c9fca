#App
quarkus.application.version=1.0.1

#Management EP
quarkus.http.port=8385
quarkus.http.host=0.0.0.0

# Other Performance Optimizations
#quarkus.thread-pool.core-threads=20
#quarkus.thread-pool.max-threads=200
#quarkus.thread-pool.queue-size=100

# Metrics and Health Checks
quarkus.datasource.metrics.enabled=true

#datasource

#Profile
quarkus.datasource."db1".db-kind=mysql
quarkus.datasource."db1".username=apps_user
quarkus.datasource."db1".password=Tb#<M#BnvBc%ur5q
quarkus.datasource."db1".jdbc.initial-size=5
quarkus.datasource."db1".jdbc.max-size=15
quarkus.datasource."db1".jdbc.driver=com.mysql.cj.jdbc.Driver
quarkus.datasource."db1".jdbc.url=jdbc:mysql://***********:3306/mossbets_profile?useSSL=false&autoreconnect=true&useUnicode=true&characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull&relaxAutoCommit=true&useServerPrepStmts=false&rewriteBatchedStatements=true&character_set_server=utf8mb4&allowPublicKeyRetrieval=true
quarkus.datasource."db1".jdbc.validation-query-sql=select 1
quarkus.datasource."db1".jdbc.pooling-enabled=true
quarkus.datasource."db1".jdbc.min-size=1
quarkus.datasource."db1".jdbc.idle-removal-interval=60S
quarkus.datasource."db1".jdbc.background-validation-interval=10
quarkus.datasource."db1".jdbc.leak-detection-interval=30
quarkus.datasource."db1".jdbc.acquisition-timeout=10
quarkus.datasource."db1".jdbc.max-lifetime=1800S

#Bonus DB
quarkus.datasource."db2".db-kind=mysql
quarkus.datasource."db2".username=apps_user
quarkus.datasource."db2".password=Tb#<M#BnvBc%ur5q
quarkus.datasource."db2".jdbc.initial-size=5
quarkus.datasource."db2".jdbc.max-size=15
quarkus.datasource."db2".jdbc.driver=com.mysql.cj.jdbc.Driver
quarkus.datasource."db2".jdbc.url=jdbc:mysql://***********:3306/mossbets_bonus?useSSL=false&autoreconnect=true&useUnicode=true&characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull&relaxAutoCommit=true&useServerPrepStmts=false&rewriteBatchedStatements=true&character_set_server=utf8mb4&allowPublicKeyRetrieval=true
quarkus.datasource."db2".jdbc.validation-query-sql=select 1
quarkus.datasource."db2".jdbc.pooling-enabled=true
quarkus.datasource."db2".jdbc.min-size=1
quarkus.datasource."db2".jdbc.idle-removal-interval=60S
quarkus.datasource."db2".jdbc.background-validation-interval=10
quarkus.datasource."db2".jdbc.leak-detection-interval=30
quarkus.datasource."db2".jdbc.acquisition-timeout=10
quarkus.datasource."db2".jdbc.max-lifetime=1800S

#Transactions DB
quarkus.datasource."db3".db-kind=mysql
quarkus.datasource."db3".username=apps_user
quarkus.datasource."db3".password=Tb#<M#BnvBc%ur5q
quarkus.datasource."db3".jdbc.initial-size=5
quarkus.datasource."db3".jdbc.max-size=15
quarkus.datasource."db3".jdbc.driver=com.mysql.cj.jdbc.Driver
quarkus.datasource."db3".jdbc.url=jdbc:mysql://***********:3306/mossbets_transactions?useSSL=false&autoreconnect=true&useUnicode=true&characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull&relaxAutoCommit=true&useServerPrepStmts=false&rewriteBatchedStatements=true&character_set_server=utf8mb4&allowPublicKeyRetrieval=true
quarkus.datasource."db3".jdbc.validation-query-sql=select 1
quarkus.datasource."db3".jdbc.pooling-enabled=true
quarkus.datasource."db3".jdbc.min-size=1
quarkus.datasource."db3".jdbc.idle-removal-interval=60S
quarkus.datasource."db3".jdbc.background-validation-interval=10
quarkus.datasource."db3".jdbc.leak-detection-interval=30
quarkus.datasource."db3".jdbc.acquisition-timeout=10
quarkus.datasource."db3".jdbc.max-lifetime=1800S

#Bets DB
quarkus.datasource."db4".db-kind=mysql
quarkus.datasource."db4".username=apps_user
quarkus.datasource."db4".password=Tb#<M#BnvBc%ur5q
quarkus.datasource."db4".jdbc.initial-size=5
quarkus.datasource."db4".jdbc.max-size=20
quarkus.datasource."db4".jdbc.driver=com.mysql.cj.jdbc.Driver
quarkus.datasource."db4".jdbc.url=jdbc:mysql://***********:3306/mossbets_bets?useSSL=false&autoreconnect=true&useUnicode=true&characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull&relaxAutoCommit=true&useServerPrepStmts=false&rewriteBatchedStatements=true&character_set_server=utf8mb4&allowPublicKeyRetrieval=true
quarkus.datasource."db4".jdbc.validation-query-sql=select 1
quarkus.datasource."db4".jdbc.pooling-enabled=true
quarkus.datasource."db4".jdbc.min-size=1
quarkus.datasource."db4".jdbc.idle-removal-interval=60S
quarkus.datasource."db4".jdbc.background-validation-interval=10
quarkus.datasource."db4".jdbc.leak-detection-interval=30
quarkus.datasource."db4".jdbc.acquisition-timeout=10
quarkus.datasource."db4".jdbc.max-lifetime=1800S

##
## Slave DB
##
#Profile Read DB
quarkus.datasource."dbr1".db-kind=mysql
quarkus.datasource."dbr1".username=apps_user
quarkus.datasource."dbr1".password=Tb#<M#BnvBc%ur5q
quarkus.datasource."dbr1".jdbc.initial-size=5
quarkus.datasource."dbr1".jdbc.max-size=15
quarkus.datasource."dbr1".jdbc.driver=com.mysql.cj.jdbc.Driver
quarkus.datasource."dbr1".jdbc.url=jdbc:mysql://***********:3306/mossbets_profile?useSSL=false&autoreconnect=true&useUnicode=true&characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull&useServerPrepStmts=false&rewriteBatchedStatements=true&character_set_server=utf8mb4&allowPublicKeyRetrieval=true
quarkus.datasource."dbr1".jdbc.validation-query-sql=select 1
quarkus.datasource."dbr1".jdbc.pooling-enabled=true
quarkus.datasource."dbr1".jdbc.min-size=1
quarkus.datasource."dbr1".jdbc.idle-removal-interval=60S
quarkus.datasource."dbr1".jdbc.background-validation-interval=10
quarkus.datasource."dbr1".jdbc.leak-detection-interval=30
quarkus.datasource."dbr1".jdbc.acquisition-timeout=10
quarkus.datasource."dbr1".jdbc.max-lifetime=1800S

#Bonus Read DB
quarkus.datasource."dbr2".db-kind=mysql
quarkus.datasource."dbr2".username=apps_user
quarkus.datasource."dbr2".password=Tb#<M#BnvBc%ur5q
quarkus.datasource."dbr2".jdbc.initial-size=5
quarkus.datasource."dbr2".jdbc.max-size=15
quarkus.datasource."dbr2".jdbc.driver=com.mysql.cj.jdbc.Driver
quarkus.datasource."dbr2".jdbc.url=jdbc:mysql://***********:3306/mossbets_bonus?useSSL=false&autoreconnect=true&useUnicode=true&characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull&useServerPrepStmts=false&rewriteBatchedStatements=true&character_set_server=utf8mb4&allowPublicKeyRetrieval=true
quarkus.datasource."dbr2".jdbc.validation-query-sql=select 1
quarkus.datasource."dbr2".jdbc.pooling-enabled=true
quarkus.datasource."dbr2".jdbc.min-size=1
quarkus.datasource."dbr2".jdbc.idle-removal-interval=60S
quarkus.datasource."dbr2".jdbc.background-validation-interval=10
quarkus.datasource."dbr2".jdbc.leak-detection-interval=30
quarkus.datasource."dbr2".jdbc.acquisition-timeout=10
quarkus.datasource."dbr2".jdbc.max-lifetime=1800S

#Transactions Read DB
quarkus.datasource."dbr3".db-kind=mysql
quarkus.datasource."dbr3".username=apps_user
quarkus.datasource."dbr3".password=Tb#<M#BnvBc%ur5q
quarkus.datasource."dbr3".jdbc.initial-size=5
quarkus.datasource."dbr3".jdbc.max-size=15
quarkus.datasource."dbr3".jdbc.driver=com.mysql.cj.jdbc.Driver
quarkus.datasource."dbr3".jdbc.url=jdbc:mysql://***********:3306/mossbets_transactions?useSSL=false&autoreconnect=true&useUnicode=true&characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull&useServerPrepStmts=false&rewriteBatchedStatements=true&character_set_server=utf8mb4&allowPublicKeyRetrieval=true
quarkus.datasource."dbr3".jdbc.validation-query-sql=select 1
quarkus.datasource."dbr3".jdbc.pooling-enabled=true
quarkus.datasource."dbr3".jdbc.min-size=1
quarkus.datasource."dbr3".jdbc.idle-removal-interval=60S
quarkus.datasource."dbr3".jdbc.background-validation-interval=10
quarkus.datasource."dbr3".jdbc.leak-detection-interval=30
quarkus.datasource."dbr3".jdbc.acquisition-timeout=10
quarkus.datasource."dbr3".jdbc.max-lifetime=1800S

#Bets Read DB
quarkus.datasource."dbr4".db-kind=mysql
quarkus.datasource."dbr4".username=apps_user
quarkus.datasource."dbr4".password=Tb#<M#BnvBc%ur5q
quarkus.datasource."dbr4".jdbc.initial-size=5
quarkus.datasource."dbr4".jdbc.max-size=15
quarkus.datasource."dbr4".jdbc.driver=com.mysql.cj.jdbc.Driver
quarkus.datasource."dbr4".jdbc.url=jdbc:mysql://***********:3306/mossbets_bets?useSSL=false&autoreconnect=true&useUnicode=true&characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull&useServerPrepStmts=false&rewriteBatchedStatements=true&character_set_server=utf8mb4&allowPublicKeyRetrieval=true
quarkus.datasource."dbr4".jdbc.validation-query-sql=select 1
quarkus.datasource."dbr4".jdbc.pooling-enabled=true
quarkus.datasource."dbr4".jdbc.min-size=1
quarkus.datasource."dbr4".jdbc.idle-removal-interval=60S
quarkus.datasource."dbr4".jdbc.background-validation-interval=10
quarkus.datasource."dbr4".jdbc.leak-detection-interval=30
quarkus.datasource."dbr4".jdbc.acquisition-timeout=10
quarkus.datasource."dbr4".jdbc.max-lifetime=1800S

#DB user
quarkus.datasource."dbr5".db-kind=mysql
quarkus.datasource."dbr5".username=apps_user
quarkus.datasource."dbr5".password=Tb#<M#BnvBc%ur5q
quarkus.datasource."dbr5".jdbc.initial-size=5
quarkus.datasource."dbr5".jdbc.max-size=5
quarkus.datasource."dbr5".jdbc.driver=com.mysql.cj.jdbc.Driver
quarkus.datasource."dbr5".jdbc.url=***********************************************************************************************************************************************************************************************************************************************************************
quarkus.datasource."dbr5".jdbc.validation-query-sql=select 1
quarkus.datasource."dbr5".jdbc.pooling-enabled=true
quarkus.datasource."dbr5".jdbc.min-size=1
quarkus.datasource."dbr5".jdbc.idle-removal-interval=60S
quarkus.datasource."dbr5".jdbc.background-validation-interval=10
quarkus.datasource."dbr5".jdbc.leak-detection-interval=30
quarkus.datasource."dbr5".jdbc.acquisition-timeout=10
quarkus.datasource."dbr5".jdbc.max-lifetime=1800S

#Activate(Write) Datasources
%db1.quarkus.datasource."db1".active=true
%db2.quarkus.datasource."db2".active=true
%db3.quarkus.datasource."db3".active=true
%db4.quarkus.datasource."db4".active=true

#Activate(Read) Datasources
%dbr1.quarkus.datasource."dbr1".active=true
%dbr2.quarkus.datasource."dbr2".active=true
%dbr3.quarkus.datasource."dbr3".active=true
%dbr4.quarkus.datasource."dbr4".active=true
%dbr5.quarkus.datasource."dbr5".active=true

quarkus.datasource.devservices.enabled=false

#logging
quarkus.log.file.level=ALL
quarkus.log.file.enable=true
quarkus.log.console.enable=false
quarkus.log.file.path=/var/log/java/mossbets.log
quarkus.log.file.format=%d{yyyy-MM-dd HH:mm:ss,SSS} : %-5p : %m%n
quarkus.log.file.rotation.max-file-size=5000M
quarkus.log.file.rotation.max-backup-index=10
quarkus.log.file.rotation.file-suffix=.yyyy-MM-dd
quarkus.log.file.rotation.rotate-on-boot=false

# Other Optimizations
quarkus.arc.remove-unused-beans=true
quarkus.live-reload.enabled=false

#QueueLiveBet
mbs.sms-outbox-queue=OUTBOX,OUTBOX_1
mbs.live-bet-queues=LIVE_BETS,LIVE_BETS_1,LIVE_BETS_2,LIVE_BETS_3,ABLY_RESPONSE,ABLY_RESPONSE_1
mbs.deposit-failed-queues=DEPOSIT_C2B_FAILED
mbs.deposit-queues=DEPOSIT_C2B,DEPOSIT_C2B_1,DEPOSIT_C2B_2,CHECKOUT_C2B
mbs.risk-approval-queues=RISK_APPROVALS,RISK_APPROVALS_1
mbs.stk-checkout-queues=CHECKOUT,CHECKOUT_1
mbs.withdrawal-queues=WITHDRAWAL,WITHDRAWAL_1
mbs.refund-queues=REFUNDS,REFUNDS_1
mbs.bet-settlement-queues=BET_SETTLEMENT,BET_SETTLEMENT_1,LOST_BET_SETTLEMENT,LOST_BET_SETTLEMENT_1
mbs.kra-queues=KRA_STAKE,KRA_RESULTS,KRA_PAYOUTS
mbs.bonus-queues=BONUS_AWARDS

#Mpesa
mbs.min-deposit=1
mbs.max-deposit=250000

#Taxation
mbs.tax-excise=0.05
mbs.tax-withholding=0.05

#Mail
mbs.mail-admin-address=${MAIL_ADMIN_ADDRESS:<EMAIL>}
mbs.mail-admin-host=${MAIL_ADMIN_HOST:smtp.mandrillapp.com}
mbs.mail-password=${MAIL_PASSWORD:cmygQM4YxFhr7jDsJ_PPEw}
mbs.mail-username=${MAIL_USERNAME:<EMAIL>}

#Redis
mbs.redis-port=6379
mbs.redis-user=${REDIS_USER:liden}
mbs.redis-auth=${REDIS_AUTH:eGuT7yrbJZ8d}
mbs.redis-host=${REDIS_HOST:ke-pr-redis-ha-1-node-0}

mbs.number-of-threads=200

#Rabbit
mbs.rabbit-mq-port=5672
mbs.rabbit-mq-password=${RABBIT_PASSWORD:lID3n}
mbs.rabbit-mq-username=${RABBIT_USERNAME:liden}
mbs.rabbit-mq-host=${RABBIT_HOST:rabbitmq-cluster-1-vm}

#Risk
mbs.risk-max-amount=1000
mbs.withdrawal-key=${WITHDRAWAL_KEY:ndjweHKWd2893eu9n098714788&^%444v}
mbs.genius-x-api-key=${GENIUS_X_API_KEY:mdnQyi8Lq42QS1jFAK1Qe1DCXQJJFgRC6DdyB9yJ}
mbs.mpesa-c2b-broker-key=${MPESA_C2B_BROKER_KEY:Y&S0TI!RL?%JhPXse_0W}
mbs.mpesa-c2b-stk-status=2

#URLS
#Sms
mbs.sms-out-url=http://**************/sms_api/v3/outgoing/{network_id}/send
#
mbs.genius-risk-url=http://ke-pr-dev-2:8089/mossbets/risk-assessment
mbs.genius-risk-bfr-url=https://gw1.risk.api.geniussports.com/v1/platforms/mossbets/bookmakers/10911/bets

#KRA{api-test,api-prd}
mbs.kra-prn-reg-url=https://{domain}.kra.go.ke/api/register
mbs.kra-prn-auth-url=https://{domain}.kra.go.ke/api/authenticate
mbs.kra-prn-gen-url=https://{domain}.kra.go.ke/api/generatePrnRequest
mbs.kra-saf-payout-url=https://api.safaricom.co.ke/mpesa/b2b/v1/remittax
mbs.kra-stake-data-transmit-url=https://{domain}.kra.go.ke/api/receiveStakeData
mbs.kra-stake-outcome-transmit-url=https://{domain}.kra.go.ke/api/receiveOutcomeData

#Mpesa Checkout
mbs.mpesa-saf-auth-url=https://api.safaricom.co.ke/oauth/v1/generate?grant_type=client_credentials
mbs.mpesa-saf-checkout-url=https://api.safaricom.co.ke/mpesa/stkpush/v1/processrequest

#docker image bulid
quarkus.container-image.registry=europe-west1-docker.pkg.dev
quarkus.container-image.group=broker-server-341612/images
quarkus.container-image.build=false
quarkus.container-image.name=mbs-consumer-app
quarkus.container-image.push=false